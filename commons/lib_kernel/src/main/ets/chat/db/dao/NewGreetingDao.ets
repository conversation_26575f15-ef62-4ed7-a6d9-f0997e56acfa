import { ContactDao } from './ContactDao';
import { Logger } from 'lib_zputils/Index';

const TAG = 'NewGreetingDao';

/**
 * 新招呼联系人数据访问对象
 * 负责构建新招呼分组相关的SQL查询
 */
export class NewGreetingDao {
  private contactDao: ContactDao;

  constructor(contactDao: ContactDao) {
    this.contactDao = contactDao;
  }

  /**
   * 构建新招呼置顶联系人查询SQL
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildNewGreetingTopContactSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND isTop = 1
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC`;
    
    Logger.debug(TAG, `buildNewGreetingTopContactSql: ${sql}`);
    return sql;
  }

  /**
   * 构建新招呼联系人查询SQL（排除置顶）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildNewGreetingContactSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND isTop = 0
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      // 根据筛选类型添加相应条件
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
        // 可以根据需要添加更多筛选条件
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC`;
    
    Logger.debug(TAG, `buildNewGreetingContactSql: ${sql}`);
    return sql;
  }

  /**
   * 构建新招呼APPEND查询SQL（向后加载更旧的数据）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildNewGreetingAppendSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND (local_chatTime < ? OR (local_chatTime = ? AND id < ?))
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC LIMIT ?`;
    
    Logger.debug(TAG, `buildNewGreetingAppendSql: ${sql}`);
    return sql;
  }

  /**
   * 构建新招呼PREPEND查询SQL（向前加载更新的数据）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildNewGreetingPrependSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND (local_chatTime > ? OR (local_chatTime = ? AND id > ?))
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime ASC, id ASC LIMIT ?`;
    
    Logger.debug(TAG, `buildNewGreetingPrependSql: ${sql}`);
    return sql;
  }

  /**
   * 查询新招呼联系人总数
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 联系人总数
   */
  public async queryNewGreetingContactCount(filterType: number, jobId: number): Promise<number> {
    let sql = `
      SELECT COUNT(*) as count FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
    `;

    // 添加筛选条件
    const params: (string | number)[] = [];
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const count = cursor.getLong(cursor.getColumnIndex('count'));
        cursor.close();
        return count;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryNewGreetingContactCount failed: ${error}`);
      return 0;
    }
  }

  /**
   * 查询新招呼联系人的总未读数量
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 未读数量
   */
  public async queryNewGreetingTotalUnreadCount(filterType: number, jobId: number): Promise<number> {
    let sql = `
      SELECT SUM(local_noneReadCount) as totalUnread FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND local_noneReadCount > 0
    `;

    const params: (string | number)[] = [];

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          // 未读筛选下，所有数据都是未读的，不需要额外条件
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const totalUnread = cursor.getLong(cursor.getColumnIndex('totalUnread'));
        cursor.close();
        return totalUnread;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryNewGreetingTotalUnreadCount failed: ${error}`);
      return 0;
    }
  }

  /**
   * 查询新招呼一周前联系人的未读数量
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 未读数量
   */
  public async queryNewGreetingWeekAgeUnreadCount(filterType: number, jobId: number): Promise<number> {
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    
    let sql = `
      SELECT SUM(local_noneReadCount) as totalUnread FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (
          -- 新招呼条件：对方发起的新招呼 OR (friendStage == 0 && chatTime > 0) 兜底处理
          (local_friendStage & 1) = 1 
          OR (local_friendStage = 0 AND local_chatTime > 0)
        )
        AND NOT (
          -- 排除双聊状态下有面试和有交换的联系人
          (local_friendStage & 4) = 4 AND (
            interviewStatus IN (1, 4, 7, 17)
            OR (phoneNumber IS NOT NULL AND phoneNumber != ''
                OR wxNumber IS NOT NULL AND wxNumber != ''
                OR resumeUrl IS NOT NULL AND resumeUrl != ''
                OR exchangeResumeStatus = 1)
          )
        )
        AND NOT (
          -- 排除我发送过消息的联系人（这些应该归到仅沟通）
          (local_friendStage & 2) = 2 OR friendType = 1
        )
        AND local_chatTime < ?
        AND local_noneReadCount > 0
    `;

    const params: (string | number)[] = [oneWeekAgo];

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          // 未读筛选下，所有数据都是未读的，不需要额外条件
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const totalUnread = cursor.getLong(cursor.getColumnIndex('totalUnread'));
        cursor.close();
        return totalUnread;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryNewGreetingWeekAgeUnreadCount failed: ${error}`);
      return 0;
    }
  }
}