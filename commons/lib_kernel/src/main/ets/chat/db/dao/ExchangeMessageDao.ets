import { ContactDao } from './ContactDao';
import { Logger } from 'lib_zputils/Index';

const TAG = 'ExchangeMessageDao';

/**
 * 有交换信息联系人数据访问对象
 * 负责构建有交换分组相关的SQL查询
 */
export class ExchangeMessageDao {
  private contactDao: ContactDao;

  constructor(contactDao: ContactDao) {
    this.contactDao = contactDao;
  }

  /**
   * 构建有交换信息置顶联系人查询SQL
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildExchangeMessageTopContactSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND local_isTop = 1
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC`;
    
    Logger.debug(TAG, `buildExchangeMessageTopContactSql: ${sql}`);
    return sql;
  }

  /**
   * 构建有交换信息联系人查询SQL（排除置顶）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildExchangeMessageContactSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND local_isTop = 0
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      // 根据筛选类型添加相应条件
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
        // 可以根据需要添加更多筛选条件
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC`;
    
    Logger.debug(TAG, `buildExchangeMessageContactSql: ${sql}`);
    return sql;
  }

  /**
   * 构建有交换信息APPEND查询SQL（向后加载更旧的数据）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildExchangeMessageAppendSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND (local_chatTime < ? OR (local_chatTime = ? AND id < ?))
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime DESC, id DESC LIMIT ?`;
    
    Logger.debug(TAG, `buildExchangeMessageAppendSql: ${sql}`);
    return sql;
  }

  /**
   * 构建有交换信息PREPEND查询SQL（向前加载更新的数据）
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns SQL查询语句
   */
  public buildExchangeMessagePrependSql(filterType: number, jobId: number): string {
    let sql = `
      SELECT * FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND (local_chatTime > ? OR (local_chatTime = ? AND id > ?))
    `;

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ${jobId}`;
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    sql += ` ORDER BY local_chatTime ASC, id ASC LIMIT ?`;
    
    Logger.debug(TAG, `buildExchangeMessagePrependSql: ${sql}`);
    return sql;
  }

  /**
   * 查询有交换信息联系人总数
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 联系人总数
   */
  public async queryExchangeMessageContactCount(filterType: number, jobId: number): Promise<number> {
    let sql = `
      SELECT COUNT(*) as count FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
    `;

    // 添加筛选条件
    const params: (string | number)[] = [];
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          sql += ` AND local_noneReadCount > 0`;
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const count = cursor.getLong(cursor.getColumnIndex('count'));
        cursor.close();
        return count;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryExchangeMessageContactCount failed: ${error}`);
      return 0;
    }
  }

  /**
   * 查询有交换信息联系人的总未读数量
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 未读数量
   */
  public async queryExchangeMessageTotalUnreadCount(filterType: number, jobId: number): Promise<number> {
    let sql = `
      SELECT SUM(local_noneReadCount) as totalUnread FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND local_noneReadCount > 0
    `;

    const params: (string | number)[] = [];

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          // 未读筛选下，所有数据都是未读的，不需要额外条件
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const totalUnread = cursor.getLong(cursor.getColumnIndex('totalUnread'));
        cursor.close();
        return totalUnread;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryExchangeMessageTotalUnreadCount failed: ${error}`);
      return 0;
    }
  }

  /**
   * 查询有交换信息一周前联系人的未读数量
   * @param filterType 筛选类型
   * @param jobId 职位ID
   * @returns 未读数量
   */
  public async queryExchangeMessageWeekAgeUnreadCount(filterType: number, jobId: number): Promise<number> {
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    
    let sql = `
      SELECT SUM(local_noneReadCount) as totalUnread FROM Contact 
      WHERE status > -1 
        AND friendId > 1000
        AND isRejectUser = 0
        AND isFiltered = 0
        AND (phoneNumber IS NOT NULL AND phoneNumber != ''
             OR wxNumber IS NOT NULL AND wxNumber != ''
             OR resumeUrl IS NOT NULL AND resumeUrl != ''
             OR exchangeResumeStatus = 1)
        AND (local_friendStage & 4) = 4
        AND interviewStatus NOT IN (1, 4, 7, 17)
        AND local_chatTime < ?
        AND local_noneReadCount > 0
    `;

    const params: (string | number)[] = [oneWeekAgo];

    // 添加筛选条件
    if (jobId > 0) {
      sql += ` AND jobId = ?`;
      params.push(jobId);
    }

    if (filterType > 0) {
      switch (filterType) {
        case 1: // 未读筛选
          // 未读筛选下，所有数据都是未读的，不需要额外条件
          break;
        case 2: // 来源筛选
          sql += ` AND itemSource = 1`;
          break;
      }
    }

    try {
      const cursor = await this.contactDao.rawQuery(sql, params);
      if (cursor && cursor.goToFirstRow()) {
        const totalUnread = cursor.getLong(cursor.getColumnIndex('totalUnread'));
        cursor.close();
        return totalUnread;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `queryExchangeMessageWeekAgeUnreadCount failed: ${error}`);
      return 0;
    }
  }
}