import { BaseDao, Property, QueryBuilder } from 'lib_dataorm'
import { ContactEntity } from '../entity/ContactEntity'
import { GlobalContext } from 'lib_dataorm';
import { ContactStatus } from '../entity/ContactBaseInfo';
import { <PERSON><PERSON>tils, DateUtils, <PERSON>rror<PERSON>til, <PERSON>gger, TLog, ZPEvents } from 'lib_zputils/Index';
import { common } from '@kit.AbilityKit';
import { ContactParam } from '../entity/ContactParam';
import { MessageStatus } from '../../logic/message/Constants';
import rdb from '@ohos.data.rdb';
import { relationalStore } from '@kit.ArkData';
import { AbnormalContact } from '../entity/AbnormalContact';
import { ContactReadUpdateDTO } from '../dto/ContactReadDTO';
import { OnlyContactDao } from './OnlyContactDao';
import { AllContactDao } from './AllContactDao';


const TAG = 'ContactDao'

/**
 * ResultSet接口定义
 */
interface ResultSetInterface {
  goToFirstRow(): boolean;
  goToNextRow(): boolean;
  getColumnIndex(columnName: string): number;
  getLong(columnIndex: number): number;
  getString(columnIndex: number): string;
  close(): void;
}

export class ContactDao {
  private eventHub: common.EventHub

  private dao: BaseDao<ContactEntity, number>
  private properties: Record<string, Property>
  private allColumns: string

  constructor(dao: BaseDao<ContactEntity, number>, context: common.Context) {
    this.dao = dao
    let entityClass = GlobalContext.getContext().getValue(GlobalContext.KEY_CLS) as Record<string, Object>
    this.properties = entityClass['ContactEntity'] as Record<string, Property>
    this.eventHub = context.eventHub
    this.allColumns = this.dao.getAllColumns().join(',')
  }

  /**
   * 发出同步联系人的事件
   */
  private sendSyncAllEvent(param: ContactParam) {
    this.eventHub.emit(ZPEvents.Contact.SyncAll, param)
  }

  async upsertAllBaseInfo(contactInfoBeans: Array<ContactEntity>): Promise<Array<ContactEntity>> {
    TLog.debug(TAG, `ContactTag upsertAllBaseInfo start ${DateUtils.getCurrentDateTime()}`)

    let db = this.dao.getDatabase()

    let ret = new Array<ContactEntity>()
    for (let item of contactInfoBeans) {
      if(item instanceof ContactEntity){
        ContactEntity.toDataBase(item)
      }

      db.beginTransaction();
      try {
        let predicates = new relationalStore.RdbPredicates(this.dao.getTableName())
        predicates.equalTo("friendId", '' + item.friendId)
        predicates.equalTo("friendSource", '' + item.friendSource)

        let stmt = db.compileStatement(predicates, this.dao.getTableName())
        this.dao.bindValues(stmt, item, ["id", "friendId", "friendSource"])

        let count = await stmt.executeUpdate()
        db.endTransaction();
        if (count > 0) {
          TLog.info(TAG, `upsertAllBaseInfo: update contact ok: friendId=${item.friendId}, friendSource=${item.friendSource}`)
          ret.push(item)
          continue
        }
      } catch (error) {
        TLog.error(TAG, `upsertAllBaseInfo: upsert contact error: friendId=${item.friendId}, friendSource=${item.friendSource}, ${JSON.stringify(error)}`)
        db.rollBack()
      }

      try {
        let rowId = await this.dao.insert(item)
        ret.push(item)
        TLog.info(TAG, `upsertAllBaseInfo: insert contact ok: rowId=${rowId}, friendId=${item.friendId}, friendSource=${item.friendSource}`)
      } catch (error) {
        TLog.error(TAG, `upsertAllBaseInfo: insert contact error: friendId=${item.friendId}, friendSource=${item.friendSource}, ${JSON.stringify(error)}`)
      }
    }

    TLog.debug(TAG, `ContactTag upsertAllBaseInfo end ${DateUtils.getCurrentDateTime()}`)
    return ret
  }

  async insertAndUpdateBatch(insertList: Array<ContactEntity>, updateList: Array<ContactEntity>) {
    if (insertList.length > 0) {
      await this.dao.insertInTxArrAsync(...insertList)
    }

    if (updateList.length > 0) {
      for (let item of updateList) {
        ContactEntity.toDataBase(item)
      }
      await this.dao.updateInTxAsync(...updateList)
    }
  }

  /**
   * 更新联系人全量数据：本地 + 服务端 (Full + Base)
   */
  async upsert(contact: ContactEntity, isContactSync = false): Promise<void> {
    //TODO 序列化丢了toDataBase方法，暂时这么写
    contact.localInfo.extStr = contact.localInfo.localField
      ? JSON.stringify(contact.localInfo.localField)
      : contact.localInfo.extStr
    TLog.info(TAG, `upsert friendSource = ${contact.friendSource}, friendId = ${contact.friendId}, extStr = ${contact.localInfo.extStr}`)
    let rowId = await this.dao.saveInTxAAsync(contact)
    TLog.info(TAG, `upsert friendSource = ${contact.friendSource}, friendId = ${contact.friendId}, rowId = ${rowId}`)
    const param = new ContactParam()
    param.friendId = contact.friendId
    param.friendSource = contact.friendSource
    param.isContactSync = isContactSync
    this.sendSyncAllEvent(param)
  }

  async upsertArr(contacts: ContactEntity[]): Promise<void> {
    await this.dao.saveInTxAAsync(...contacts)
  }

  async updateLastMessageState(friendId: number, friendSource: number, state: number) {
    await this.updateSingleColumn(friendId, friendSource, 'local_msgState', state)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateLastText(friendId: number, friendSource: number, text: string|null|undefined, mid: number) {
    let sql = "UPDATE Contact SET local_summary=? WHERE friendId=? AND friendSource=? AND local_mid<=?"
    await this.dao.execSql(sql, [text, friendId, friendSource, mid])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateLastTextAndState(friendId: number, friendSource: number, text: string|null|undefined, state: number, mid: number) {
    let sql = "UPDATE Contact SET local_mid=?, local_summary=?, local_msgState=? WHERE friendId=? AND friendSource=? AND local_mid<=?"
    await this.dao.execSql(sql, [mid, text, state, friendId, friendSource, mid])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async setUnreadCount(friendId: number, friendSource: number, unreadCount: number) {
    await this.updateSingleColumn(friendId, friendSource, 'local_noneReadCount', unreadCount)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async setAllMessagesRead(friendId: number, friendSource: number, unreadMid: number, isContactSync = true) {
    let sql = `UPDATE Contact
      SET local_noneReadCount=0
      WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    param.isContactSync = isContactSync
    this.sendSyncAllEvent(param)
  }

  private getUndeletedQuery(): QueryBuilder<ContactEntity> {
    return this.dao.queryBuilder().where(this.properties['status'].gt(ContactStatus.Del))
  }

  async listAll(): Promise<Array<ContactEntity>> {
    let query = this.getUndeletedQuery() // 可用联系人
    return await query.list()
  }

  async getUndeletedContactCount() {
    return this.getUndeletedQuery().count()
  }

  async getUndeletedContacts(offset: number, limit: number): Promise<Array<ContactEntity>> {
    let sql = `SELECT ${this.allColumns} FROM Contact WHERE status > ${ContactStatus.Del} LIMIT ${limit} OFFSET ${offset}`
    let cursor: rdb.ResultSet = await this.dao.rawQuery(sql)
    return this.dao.loadAllAndCloseCursor(cursor)
  }

  /**
   * 根据好友ID获取联系人
   */
  async getByFriendIdAndSource(friendId: number, friendSource: number): Promise<ContactEntity |null> {
    let query = this.dao.queryBuilder()
      .where(this.properties['friendId'].eq(friendId))
      .where(this.properties['friendSource'].eq(friendSource))
      .limit(1)
    let list: Array<ContactEntity> = await query.list()
    return list.length > 0 ? list[0] : null
  }

  async getByFriendIdAndSourceArr(friendIds: number[], friendSources: number[]): Promise<Array<ContactEntity>> {
    let sql = `SELECT ${this.allColumns} FROM Contact WHERE `
    for (let i=0; i<friendIds.length; i++) {
      sql += `(friendId=${friendIds[i]} AND friendSource=${friendSources[i]})`
      if (i != friendIds.length - 1) {
        sql += ' OR '
      }
    }

    let cursor: rdb.ResultSet = await this.dao.rawQuery(sql)
    return this.dao.loadAllAndCloseCursor(cursor)
  }

  async collect(friendId: number, friendSource: number) {
    this.updateRejectInfo(friendId, friendSource, false, '', true)
  }

  async unCollect(friendId: number, friendSource: number) {
    this.updateCollect(friendId, friendSource, false)
  }

  private async updateCollect(friendId: number, friendSource: number, collect: boolean) {
    await this.updateSingleColumn(friendId, friendSource, 'isStar', collect)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateTop(friendId: number, friendSource: number, isTop: boolean) {
    await this.updateSingleColumn(friendId, friendSource, 'isTop', isTop)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateLabels(friendId: number, friendSource: number, labels: string) {
    await this.updateSingleColumn(friendId, friendSource, 'labels', `${labels}`)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateNote(friendId: number, friendSource: number, note: string) {
    await this.updateSingleColumn(friendId, friendSource, 'note', note)
  }

  async updateBlack(friendId: number, friendSource: number, isBlack: boolean) {
    await this.updateSingleColumn(friendId, friendSource, 'isBlack', isBlack)
  }

  async updatePhoneExchangeTime(friendId: number, friendSource: number, time: number) {
    await this.updateSingleColumn(friendId, friendSource, 'exchangePhoneTime', time)
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateRejectInfo(friendId: number, friendSource: number, isRejectUser: boolean, rejectDesc: string, isStar: boolean) {
    let sql = `UPDATE Contact SET isRejectUser=${isRejectUser}, rejectDesc=?, isStar=${isStar} WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql, [rejectDesc])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateDraft(friendId: number, friendSource: number, draft: string, quoteMessageId: number) {
    let sql = `UPDATE Contact SET local_draft=?, local_quoteMid=${quoteMessageId} WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql, [draft])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  async updateExchangeResumeStatus(friendId: number, friendSource: number, exchangeResumeStatus: number) {
    await this.updateSingleColumn(friendId, friendSource, 'exchangeResumeStatus', exchangeResumeStatus)
  }

  async updatePhoneNumber(friendId: number, friendSource: number, phoneNumber: string) {
    await this.updateSingleColumn(friendId, friendSource, 'phoneNumber', phoneNumber)
  }

  async updateWxNumber(friendId: number, friendSource: number, wxNumber: string, sync: boolean = false) {
    await this.updateSingleColumn(friendId, friendSource, 'wxNumber', wxNumber)
    if (sync) {
      const param = new ContactParam()
      param.friendId = friendId
      param.friendSource = friendSource
      this.sendSyncAllEvent(param)
    }
  }

  private async updateSingleColumn(friendId: number, friendSource: number, columnName: string,
    columnValue: Object | null | undefined) {
    let sql = `UPDATE Contact SET ${columnName}=? WHERE friendId=${friendId} AND friendSource=${friendSource}`
    await this.dao.execSql(sql, [columnValue])
  }

  async deleteContact(friendId: number, friendSource: number, isSync = true) {
    await this.deleteSingleColumn(friendId, friendSource)
    if (isSync) {
      const param = new ContactParam()
      param.friendId = friendId
      param.friendSource = friendSource
      param.isDelete = true
      this.sendSyncAllEvent(param)
    }
  }

  async deleteContacts(friendIds: number[], friendSources: number[]) {
    let db = this.dao.getDatabase()

    db.beginTransaction();
    try {
      let batchSize: number = 200
      for (let start = 0, end = start + batchSize; start < friendIds.length; start = end, end += batchSize) {
        if (end > friendIds.length) {
          end = friendIds.length
        }

        let sql = `DELETE FROM Contact WHERE `
        for (let i = start; i < end; i++) {
          sql += `(friendId=${friendIds[i]} AND friendSource=${friendSources[i]})`
          if (i != end - 1) {
            sql += ' OR '
          }
        }

        await this.dao.execSql(sql)
      }

      db.endTransaction();
    } catch (e) {
      TLog.error(TAG, `deleteContacts failed: ${ErrorUtil.toString(e)}`)
      db.rollBack()
      throw e as Error
    }
  }

  async deleteAll() {
    await this.dao.deleteAllAsync()
  }

  async countAll() {
    return await this.dao.queryBuilder().count()
  }

  private async deleteSingleColumn(friendId: number, friendSource: number) {
    let query = this.dao.queryBuilder()
      .where(this.properties['friendId'].eq(friendId))
      .where(this.properties['friendSource'].eq(friendSource)).buildDelete()
    query.executeDeleteWithoutDetachingEntities()
  }

  public async fixSendingContact() {
    let sql = `UPDATE Contact SET local_msgState=${MessageStatus.Failure} WHERE local_msgState=${MessageStatus.Sending}`
    await this.dao.execSql(sql)
  }

  async listAllDeleted(): Promise<Array<Array<number>>> {
    let query = this.dao.queryBuilder().where(this.properties['status'].eq(ContactStatus.Del))
    const list: Array<ContactEntity> = await query.list()
    const result = new Array<Array<number>>()
    list.forEach((item) => {
      if (item) {
        result.push([item.friendId, item.friendSource])
      }
    })
    return result
  }

  async replaceAllContacts(contactInfoBeans: Array<ContactEntity>) {
    const sql = `UPDATE Contact SET status = -1`
    await this.dao.execSql(sql)
    await this.upsertAllBaseInfoNew(contactInfoBeans)
  }

  async upsertAllBaseInfoNew(contactInfoBeans: Array<ContactEntity>) {
    let updateList: Array<ContactEntity> = new Array<ContactEntity>()
    for (let item of contactInfoBeans) {
      try {
        item.localInfo.toDataBase()
        let rowId = await this.dao.insert(item)
        TLog.debug(TAG, `insert contact ok: rowId=${rowId}, friendId=${item.friendId}, friendSource=${item.friendSource}`)
      } catch (error) {
        try {
          let dbContact = await this.getByFriendIdAndSource(item.friendId, item.friendSource)
          if (dbContact) {
            item.id = dbContact.id
            item.localInfo = dbContact.localInfo
            updateList.push(item)
            TLog.debug(TAG, `update contact ok: friendId=${item.friendId}, friendSource=${item.friendSource}`)
          }
        } catch (error) {
          TLog.error(TAG, `upsert contact error: friendId=${item.friendId}, friendSource=${item.friendSource}, ${JSON.stringify(error)}}`)
        }
      }
    }


    if (CheckUtils.isNotEmptyArr(updateList)) {
      for (let item of updateList) {
        try {
          await this.dao.updateAsync(item)
        } catch (error) {
          TLog.error(TAG, `upsert contact error: friendId=${item.friendId}, friendSource=${item.friendSource}, ${JSON.stringify(error)}}`)
          this.updateSingleColumn(item.friendId, item.friendSource, "status", 0)
        }
      }
    }
  }

  async listAllNoLastMsgContact(): Promise<Array<ContactEntity>> {
    let query = this.dao.queryBuilder().where(this.properties['local_summary'].isNull()) // 最后一条消息为空
    return await query.list()
  }

  async updateLastMessage(mid: number,
    chatTime: number,
    datetime: number,
    summary: string,
    msgState: number,
    cmid: number,
    friendId: number,
    friendSource: number
  ) {
    let sql = `UPDATE Contact SET
    local_mid=?,
    local_chatTime=?,
    datetime=?,
    local_summary=?,
    local_msgState=?,
    local_cmid=?,
    WHERE friendId=? AND friendSource=? AND local_summary is NULL`
    await this.dao.execSql(sql, [mid, chatTime, datetime, summary, msgState, cmid, friendId, friendSource])
  }

  async updateInterviewStatus(friendId: number, friendSource: number, status: number, desc: string, url: string, dataStr: string, timeStr: string) {
    let sql = `UPDATE Contact
      SET
        interviewStatus=?,
        interviewStatusDesc=?,
        interviewUrl=?,
        interviewDateStr=?,
        interviewTimeStr=?
      WHERE
        friendId=? AND friendSource=?`
    await this.dao.execSql(sql, [status, desc, url, dataStr, timeStr, friendId, friendSource])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    param.isContactSync = false
    this.sendSyncAllEvent(param)
  }

  async updateNameAndHeadUrl(friendId: number, friendSource: number, avatar: string, name: string) {
    let sql = `UPDATE Contact SET tinyUrl=?, name=? WHERE friendId=? AND friendSource=?`
    await this.dao.execSql(sql, [avatar, name, friendId, friendSource])
    const param = new ContactParam()
    param.friendId = friendId
    param.friendSource = friendSource
    this.sendSyncAllEvent(param)
  }

  private static readonly INVALID_CONTACT_WHERE_CONDITION = `
    (local_extStr NOTNULL AND local_extStr != '' AND local_extStr NOT LIKE '{%')
    OR (interviewDateStr NOTNULL AND interviewDateStr LIKE '%//%')
    OR (videoResumeUrl NOTNULL AND videoResumeUrl != '' AND videoResumeUrl NOT LIKE '%//%')
    OR (interviewUrl NOTNULL AND interviewUrl != '' AND interviewUrl NOT LIKE '%//%')
    OR (resumeUrl NOTNULL AND resumeUrl != '' AND resumeUrl NOT LIKE '%//%')
  `

  async getAbnormalContactCount() {
    let sql = `SELECT COUNT(*) AS total FROM Contact WHERE ${ContactDao.INVALID_CONTACT_WHERE_CONDITION}`
    let cursor: relationalStore.ResultSet = await this.dao.rawQuery(sql)
    if (cursor && cursor.goToFirstRow()) {
      return cursor.getLong(cursor.getColumnIndex('total'))
    } else {
      return 0
    }
  }

  async getAbnormalContacts(): Promise<Array<AbnormalContact>> {
    const selectedColumns = [
      'id', 'friendId', 'friendSource', 'name', 'local_extStr', 'interviewDateStr',
      'videoResumeUrl', 'interviewUrl', 'resumeUrl'
    ].join(',')

    let sql = `SELECT ${selectedColumns} FROM Contact WHERE ${ContactDao.INVALID_CONTACT_WHERE_CONDITION}`
    let cursor: relationalStore.ResultSet = await this.dao.rawQuery(sql)

    const results: Array<AbnormalContact> = []
    try {
      while (cursor.goToNextRow()) {
        results.push({
          id: cursor.getLong(cursor.getColumnIndex('id')),
          friendId: cursor.getLong(cursor.getColumnIndex('friendId')),
          friendSource: cursor.getLong(cursor.getColumnIndex('friendSource')),
          name: cursor.getString(cursor.getColumnIndex('name')),
          local_extStr: cursor.getString(cursor.getColumnIndex('local_extStr')),
          interviewDateStr: cursor.getString(cursor.getColumnIndex('interviewDateStr')),
          videoResumeUrl: cursor.getString(cursor.getColumnIndex('videoResumeUrl')),
          interviewUrl: cursor.getString(cursor.getColumnIndex('interviewUrl')),
          resumeUrl: cursor.getString(cursor.getColumnIndex('resumeUrl'))
        })
      }
    } catch (error) {
      TLog.error(TAG, `getAbnormalContacts error: ${JSON.stringify(error)}}`)
    } finally {
      cursor.close()
    }

    return results
  }

  async batchUpdateUnreadCntAndLastMsgStatus(list: Array<ContactReadUpdateDTO>): Promise<ContactParam[]> {
    let db = this.dao.getDatabase()

    let updatedContacts: ContactParam[] = []

    db.beginTransaction();
    try {
      for (let dto of list) {
        let sql = `UPDATE Contact
          SET local_noneReadCount=?, local_msgState=?
          WHERE
            friendId=? AND friendSource=?`
        await this.dao.execSql(sql, [dto.unReadCount, dto.lastMsgStatus, dto.friendId, dto.friendSource])

        const param = new ContactParam()
        param.friendId = dto.friendId
        param.friendSource = dto.friendSource
        param.batchHandle = true
        updatedContacts.push(param)
      }
      db.endTransaction()
    } catch (e) {
      TLog.error(TAG, `batchUpdateUnreadCntAndLastMsgStatus failed: ${ErrorUtil.toString(e)}`)
      db.rollBack()
    } finally {
      return updatedContacts
    }
  }

  /**
   * 执行原始SQL查询并返回ContactEntity数组
   * 用于分页查询等复杂场景
   */
  async rawQueryContacts(sql: string, params: Array<string | number | boolean | null | undefined>): Promise<Array<ContactEntity>> {
    let cursor: rdb.ResultSet = await this.dao.rawQuery(sql, params)
    return this.dao.loadAllAndCloseCursor(cursor)
  }

  // 本地虚拟联系人ID列表
  private readonly LOCAL_CONTACT_IDS = [
    9007199254740990, // CHAT_POSITION_REQUEST_SETTING
    9007199254740989, // CHAT_811_VIP_HIGH_GEEK
    9007199254740988, // CHAT_UNFIT
    9007199254740987, // CHAT_INFORMITY_GEEK
    9007199254740986  // CHAT_CATCH_BACK_CHAT
  ];


   getLocalContactIds(): Array<number> {
    return this.LOCAL_CONTACT_IDS;
  }

  //rawQuery
  async rawQuery(sql: string, params: Array<string | number | boolean | null | undefined>): Promise<relationalStore.ResultSet> {
     return this.dao.rawQuery(sql, params);
  }
}