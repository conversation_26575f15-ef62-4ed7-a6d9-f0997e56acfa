import { relationalStore } from '@kit.ArkData';
import { ContactEntity } from '../entity/ContactEntity';
import { ContactDao } from './ContactDao';

/**
 * 全部联系人相关的数据访问对象
 * 专门处理全部分组的SQL查询逻辑
 */
export class AllContactDao {
  private contactDao: ContactDao;

  constructor(contactDao: ContactDao) {
    this.contactDao = contactDao;
  }

  /**
   * 构建全部联系人向后加载SQL (加载更旧的数据)
   */
  buildAllContactAppendSql(filterType?: number, jobId?: number): string {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    return `
      SELECT * FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        AND isTop = 0
        ${filterConditions}
        AND (
          local_chatTime < ?
          OR (
            local_chatTime = ? AND id < ?
          )
        )
      ORDER BY
        local_chatTime DESC,
        id DESC
      LIMIT ?
    `;
  }

  /**
   * 构建全部联系人向前加载SQL (加载更新的数据)
   */
  buildAllContactPrependSql(filterType?: number, jobId?: number): string {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    return `
      SELECT * FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        AND isTop = 0
        ${filterConditions}
        AND (
          local_chatTime > ?
          OR (
            local_chatTime = ? AND id > ?
          )
        )
      ORDER BY
        local_chatTime ASC,
        id ASC
      LIMIT ?
    `;
  }

  /**
   * 查询全部联系人总数
   */
  async queryAllContactCountSql(filterType?: number, jobId?: number): Promise<number> {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    const querySql = `
      SELECT COUNT(*) as count FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        ${filterConditions}
    `;

    let cursor: relationalStore.ResultSet = await this.contactDao.rawQuery(querySql, []);
    try {
      if (cursor && cursor.goToFirstRow()) {
        return cursor.getLong(cursor.getColumnIndex('count'));
      }
      return 0;
    } finally {
      cursor?.close();
    }
  }

  /**
   * 构建全部联系人置顶查询SQL
   * 查询所有置顶联系人
   */
  buildAllContactTopSql(filterType?: number, jobId?: number): string {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    return `
      SELECT * FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        AND isTop = 1
        ${filterConditions}
      ORDER BY
        local_chatTime DESC,
        id DESC
    `;
  }

  /**
   * 构建全部联系人非置顶查询SQL
   * 查询所有非置顶联系人，用于混合数据加载
   */
  buildAllContactNonTopSql(filterType?: number, jobId?: number): string {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    return `
      SELECT * FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        AND isTop = 0
        ${filterConditions}
      ORDER BY
        local_chatTime DESC,
        id DESC
      LIMIT ?
    `;
  }

  /**
   * 查询全部联系人中一周前联系人的未读数量总和
   */
  async queryAllContactWeekAgeUnreadCount(filterType?: number, jobId?: number): Promise<number> {
    let filterConditions = '';

    // 添加筛选条件
    if (filterType === 1) { // F2ContactFilterType.Unread
      filterConditions += ' AND local_noneReadCount > 0';
    } else if (filterType === 2) { // F2ContactFilterType.Source
      filterConditions += ' AND itemSource = 1';
    }

    // 添加职位筛选
    if (jobId && jobId > 0) {
      filterConditions += ` AND jobId = ${jobId}`;
    }

    // 计算7天前的时间戳
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    const querySql = `
      SELECT SUM(local_noneReadCount) as totalUnread FROM Contact
      WHERE status > -1
        AND friendId > 1000
        AND friendId NOT IN (${this.contactDao.getLocalContactIds().join(',')})
        AND local_chatTime > 0
        AND local_chatTime < ${sevenDaysAgo}
        AND local_noneReadCount > 0
        ${filterConditions}
    `;

    let cursor: relationalStore.ResultSet = await this.contactDao.rawQuery(querySql, []);
    try {
      if (cursor && cursor.goToFirstRow()) {
        const totalUnread = cursor.getLong(cursor.getColumnIndex('totalUnread'));
        return totalUnread || 0;
      }
      return 0;
    } finally {
      cursor?.close();
    }
  }
}
