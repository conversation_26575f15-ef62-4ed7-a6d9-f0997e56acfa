import { HashMap, util } from '@kit.ArkTS';
import { ServerJobBean } from 'lib_zpapi/Index';
import FilterManager, {
    BossObtainRejectFilter,
    BossObtainStarFilter,
    BossRemoveFollowChatFilter,
    GeekObtainUnInterestFilter,
    ObtainFilteredUserFilter,
    ObtainNoneReadFilter,
    ObtainNoneRTypeCountFilter,
    RemoveFilteredUserFilter,
    RemoveRejectFilter
} from './FilterManager';
import { ChatUrlConfig, HttpResponse, POST, SuccessResponse, URLConfig, ZPHttpError } from 'lib_http/Index';
import {
  CheckUtils,
  CommonUtil,
  DateUtils,
  LList,
  Logger,
  TextUtils,
  TLog,
  ToastUtils,
  ZPEvents
} from 'lib_zputils/Index';
import F2ContactModeManager from './F2ContactModeManager';
import { ContactSortManager } from './ContactSortManager';
import { GreetSortListResponse } from '../http/GreetSortListResponse';
import { MessageReadResponse } from '../http/MessageReadResponse';
import { MessageReadBean } from './bean/MessageReadBean';
import { common } from '@kit.AbilityKit';
import { ContactEntity, GroupType } from '../db/entity/ContactEntity';
import { Kernel } from '../../Kernel';
import NotifyUtils from '../NotifyUtils';
import { CommonConfigManager } from '../../config/custom/CommonConfigManager';
import { ContactParam } from '../db/entity/ContactParam';
import ContactClassManager from '../ContactClassManager';
import { FriendSource } from '../db/entity/ContactBaseInfo';
import { asyncLoadAllContacts } from '../db/ChatDbWorker';
import { SetTopContactResponse } from '../../api/SetTopContactResponse';
import { F2ContactFilterType } from './F2ContactFilterType';
import { ContactGroupBean } from './bean/ContactGroupBean';
import { UserGrayFeatureManager } from '../../utils/gray/UserGrayFeatureManager';
import { MqttConfig } from '../MqttConfig';
import { ReadBean, ReadMessage } from '../logic/message/model/ReadMessage';
import { DefaultCallback, SenderHandler } from '../SenderHandler';
import { BaseFilter } from './bean/BaseFilter';
import { GeekFilterData } from './bean/GeekFilterData';
import { BossFilterData } from './bean/BossFilterData';
import { ContactUtils } from '../ContactUtils';
import { ContactPageData } from '../sync/entity/ContactPageData';
import { ReaderHandler } from '../ReaderHandler';
import { MessageStatus } from '../logic/message/Constants';
import { ChatAnalyzer } from '../messageHandler/ChatAnalyzer';
import { VirtualContactManager } from '../paging/VirtualContactManager';


const TAG = 'F2ContactManager'

export class F2ContactManager {
  //全部
  private all: ContactGroupBean = new ContactGroupBean(GroupType.ALL);

  //新招呼
  private new_greeting: ContactGroupBean = new ContactGroupBean(GroupType.NEW_GREETING);

  //仅沟通
  private only_contact: ContactGroupBean = new ContactGroupBean(GroupType.ONLY_CONTACT);

  //有交换
  private exchange_message: ContactGroupBean = new ContactGroupBean(GroupType.EXCHANGE_MESSAGE);

  //有面试
  private have_interview: ContactGroupBean = new ContactGroupBean(GroupType.HAVE_INTERVIEW);

  //分组map
  private groupMap: HashMap<number, ContactGroupBean> = new HashMap();

  //b端筛选职位
  private jobBean?: ServerJobBean;

  unreadCount = 0

  public isTopExpandState = true;

  public allContact: Array<ContactEntity> = []

  private eventHub = AppStorage.get<common.Context>('context')?.eventHub
  private isLoading: boolean = false
  private hasIgnore: boolean = false

  private isSupportItemSourceFilter: boolean = false
  private hasFastHandleContact: boolean = false

  public isInLoading() {
    return this.isLoading
  }

  private isFilterOption: boolean = false
  private selectFilter = F2ContactFilterType.None
  public multiFilter?: BaseFilter;

  public async initContactData(retryLast: boolean = false) {
    if (retryLast) {
      Logger.info(TAG, `initContactData forceSync and hasIgnore=${this.hasIgnore}`)
      if (this.hasIgnore) {
        this.hasIgnore = false
      } else {
        return
      }
    } else if (this.isLoading) {
      Logger.info(TAG, `initContactData isLoading`)
      this.hasIgnore = true
      return
    }

    this.isLoading = true

    let start = Date.now()
    let uuid = util.generateRandomUUID(false)
    Logger.debug(TAG, `initContactData start ${uuid}`)
    const newContacts = await asyncLoadAllContacts()
    let loadEnd = Date.now()
    Logger.debug(TAG, `initContactData end ${uuid}, count=${this.allContact.length}, cost=${loadEnd - start}`)

    this.refreshContacts(newContacts)
    Logger.debug(TAG, `refreshContacts end ${uuid}, count=${this.allContact.length}, cost=${Date.now() - loadEnd}`)

    this.eventHub?.emit(ZPEvents.Contact.LoadContact)

    this.isLoading = false
  }

  private async refreshContacts(newContacts: ContactEntity[]) {
    if (this.contactsChangedWhileLoading) {
      TLog.info(TAG, `refreshContacts: from ${this.allContact.length} to ${newContacts.length}`)
      this.allContact.forEach(old => {
        const exists = newContacts.some(newContact => newContact.friendId === old.friendId &&
          newContact.friendSource === old.friendSource
        )

        if (!exists) {
          TLog.info(TAG, `refreshContacts: add old contact ${JSON.stringify(old)}`)
          newContacts.push(old)
        }
      })
      TLog.info(TAG, `refreshContacts: result ${newContacts.length}`)
      this.contactsChangedWhileLoading = false
    }

    this.allContact = newContacts
  }

  private reported: boolean = false
  private contactsChangedWhileLoading: boolean = false
  public async updateContact(param: ContactParam) {
    if (this.isLoading && !this.contactsChangedWhileLoading) {
      this.contactsChangedWhileLoading = true
      TLog.info(TAG, `updateContact while loading, param ${JSON.stringify(param)}`)

      if (!this.reported) {
        ChatAnalyzer.report('contactsChangedWhileLoading', JSON.stringify(param))
        this.reported = true
      }
    }

    if (param.isDelete) {
      const deleteIndex =
        this.allContact.findIndex(item => (item.friendId == param.friendId && item.friendSource == param.friendSource))
      if (deleteIndex >= 0) {
        this.allContact.splice(deleteIndex, 1)
      }
    } else {
      const contactDao = await Kernel.getInstance().dbService().getContactDao()
      if (contactDao) {
        if (param.friendId > 0) {
          const contact = await contactDao.getByFriendIdAndSource(param.friendId, param.friendSource)
          if (contact) {
            const updateIndex = this.allContact.findIndex(item => (item.friendId == param.friendId &&
              item.friendSource == param.friendSource))
            if (updateIndex < 0) {
              this.allContact.push(contact)
            } else {
              this.allContact[updateIndex] = contact
            }
          }
        }
      }
    }
  }

  public async updateLocalContact(contact: ContactEntity) {
    if (contact) {
      const updateIndex = this.allContact.findIndex(item => (item.friendId == contact.friendId &&
        item.friendSource == contact.friendSource))
      if (updateIndex < 0) {
        this.allContact.push(contact)
      } else {
        this.allContact[updateIndex] = contact
      }
    }
  }

  public async updateContacts(params: Array<ContactParam>) {
    for (let param of params) {
      await this.updateContact(param)
    }
  }

  public deleteAll() {
    this.allContact = []
  }

  public getAllContacts(): Array<ContactEntity> {
    const contacts = F2ContactModeManager.getAllContact()
    const removeFilteredUserFilter = new RemoveFilteredUserFilter()
    const bossRemoveFollowChatFilter = new BossRemoveFollowChatFilter()
    const removeRejectFilter = new RemoveRejectFilter()
    const result = FilterManager.getResult(contacts,
      removeFilteredUserFilter,
      bossRemoveFollowChatFilter, removeRejectFilter)
    return ContactSortManager.executeDefaultSort(result);
  }

  private initialized: boolean = false

  private init() {
    if (this.isSupportRecommendSort(this.new_greeting.groupType)) {
      this.new_greeting.sortType = CommonConfigManager.getInstance().getGreetingRecSort()
    }
  }

  //获取分组map
  public async getContactGroupMap() {
    if (!this.initialized) {
      this.initialized = true
      this.init()
    }
    await this.refreshGroupMap()
    return this.groupMap
  }

  //刷新分组map
  private async refreshGroupMap() {
    let contacts = this.getAllContacts()
    if (!contacts) {
      return
    }
    //清空全部数据
    this.resetList();

    //筛选职位
    contacts = this.filterContact(contacts)

    //todo c端联系人筛选底部弹窗需求在加
    //B/C 筛选
    // this.multiFilter = this.createMultiFilterData();
    // if (this.multiFilter) {
    //   this.multiFilter.filterData(contacts);
    // }

    if (CheckUtils.isEmptyArr(contacts)) { // 筛选后无数据
      this.groupMap.set(GroupType.ALL, this.all);
      if (this.selectFilter == F2ContactFilterType.None && !this.jobBean) { //过滤数据
        return;
      }
      if (this.selectFilter > F2ContactFilterType.None) { // 还原筛选项
        this.selectFilter = F2ContactFilterType.None
      } else if (this.jobBean) {
        this.jobBean = undefined;
      }

      //还原首选项 出现组织数据
      await this.refreshGroupMap();
      return;
    }
    //循环遍历分组数据
    for (const c of contacts) {
      if (!c) {
        continue
      }
      //全部分组添加联系人
      this.all.addContact(c)
      //其他分组添加联系人
      this.addChildGroup(c)
    }
    //设置分组数据,没有数据不对外返回
    this.setGroupList();
  }

  //其他分组添加联系人
  private async addChildGroup(contactBean: ContactEntity) {
    //本地联系人，GEEK_WAIT_ACHIEVE本地联系人需要在仅沟通展示
    if (F2ContactModeManager.isLocalContact(contactBean.friendId) && contactBean.friendId != MqttConfig.GEEK_WAIT_ACHIEVE) return;
    //系统联系人
    if (F2ContactModeManager.isServerSysContact(contactBean.friendId)) return;

    //Boss是否显示道具筛选
    this.isSupportItemSourceFilter = Kernel.getInstance().isBossRole() && (this.isSupportItemSourceFilter || contactBean.itemSource == 1);

    //是否有可处理的单聊联系人
    if (contactBean.localInfo.noneReadCount > 0) {
      this.hasFastHandleContact = true;
    }

    switch (ContactClassManager.getGroupType(contactBean)) {
    //有面试
      case GroupType.HAVE_INTERVIEW:
        this.have_interview.addContact(contactBean);
        break;
    //有交换
      case GroupType.EXCHANGE_MESSAGE:
        this.exchange_message.addContact(contactBean);
        break;
    //仅沟通
      case GroupType.ONLY_CONTACT:
        this.only_contact.addContact(contactBean);
        break;
    //新招呼
      case GroupType.NEW_GREETING:
        this.new_greeting.addContact(contactBean);
        break;
    }
  }

  //清空数据
  public resetList() {
    this.groupMap.clear()
    this.all.clear()
    this.new_greeting.clear();
    this.only_contact.clear();
    this.exchange_message.clear();
    this.have_interview.clear();
    this.isSupportItemSourceFilter = false;
    this.hasFastHandleContact = false;
  }

  //筛选联系人
  private filterContact(contacts: Array<ContactEntity>) {
    let filterContact: Array<ContactEntity> = contacts.filter(item => {
      if (this.jobBean && item.jobId != this.jobBean?.jobId) {
        return false
      } else if (this.selectFilter == F2ContactFilterType.Unread && ContactClassManager.getUnreadCount(item) == 0) {
        return false
      } else if (this.selectFilter == F2ContactFilterType.Source && item.itemSource != 1) {
        return false
      } else {
        return true
      }
    })
    return filterContact
  }

  //groupMap塞各分组数据
  private setGroupList() {
    if (this.all.hasData()) {
      if (!this.isFilterOption) {
        let newContactReadUser = UserGrayFeatureManager.getNewContactReadUser()
        let newContactUnReadUser = UserGrayFeatureManager.getNewContactUnReadUser()
        this.isFilterOption = newContactReadUser > 0 && this.getAllContactCount() > newContactReadUser
          && this.all.noneReadContactCount > newContactUnReadUser;
      }
      this.groupMap.set(GroupType.ALL, this.all);
    }

    if (this.new_greeting.hasData()) {
      //1121.91【B/C】新招呼消息的【推荐排序】模式@笑笑@周鹏祖@郭俊威@邹东@陈青云@姬帅@刘鹏飞
      if (this.isSupportNewGreetingRecommendSort(this.new_greeting.groupType)) {
        this.recommendSortData(this.new_greeting);
      }
      this.new_greeting.sortSortPosition()
      this.groupMap.set(GroupType.NEW_GREETING, this.new_greeting);
    }

    if (this.only_contact.hasData()) {
      this.groupMap.set(GroupType.ONLY_CONTACT, this.only_contact);
    }

    if (this.exchange_message.hasData()) {
      this.groupMap.set(GroupType.EXCHANGE_MESSAGE, this.exchange_message);
    }

    if (this.have_interview.hasData()) {
      this.groupMap.set(GroupType.HAVE_INTERVIEW, this.have_interview);
    }
  }

  public isSupportNewGreetingRecommendSort(groupType: number) {
    if (groupType == GroupType.NEW_GREETING && NotifyUtils.isNewContactCommon()) {
      return CommonConfigManager.getInstance().getGreetingRecSort() > 0;
    }
    return false;
  }

  public hasAllData(): boolean {
    return this.all.hasData()
  }

  public getAllContactCount() {
    return this.all.contactBeans.length
  }

  public getTopContactCount() {
    return this.all.contactTops.length
  }

  public getTopNoneReadCount() {
    const filter = new ObtainNoneReadFilter()
    const result = FilterManager.getResult(this.all.contactTops, filter)
    return result.length
  }

  //获取分组名字
  public getTypeName(groupType: number): string {
    switch (groupType) {
    //有面试
      case GroupType.HAVE_INTERVIEW:
        return "有面试";
    //有交换
      case GroupType.EXCHANGE_MESSAGE:
        return "有交换";
    //仅沟通
      case GroupType.ONLY_CONTACT:
        return "仅沟通";
    //新招呼
      case GroupType.NEW_GREETING:
        return "新招呼";
    }
    if (this.jobBean) {
      return this.jobBean.positionName;
    }
    return "全部";
  }

  //对外暴露 返回全部分组数据Map, 通过下面key
  public getContactGroupData(groupType: number) {
    return this.groupMap.get(groupType);
  }

  public isNewContactRecommendSort(groupType: number) {
    if (this.isSupportRecommendSort(groupType)) {
      const contactGroupData = this.getContactGroupData(groupType);
      if (!contactGroupData) {
        return CommonConfigManager.getInstance().getGreetingRecSort() == ContactGroupBean.RECOMMEND_SORT_TYPE;
      }
      return contactGroupData.sortType == ContactGroupBean.RECOMMEND_SORT_TYPE;
    }
    return false
  }

  //是否有筛选职位
  public isFilterJob() {
    return this.jobBean != undefined
  }

  //判断是否有单聊联系人：搜索位置有单聊才会显示入口
  public hasSingleContact() {
    return this.isFilterJob() || this.new_greeting.hasData()
      || this.only_contact.hasData() || this.exchange_message.hasData()
      || this.have_interview.hasData();
  }

  //获取当前职位id
  public getSelectJobId() {
    return this.jobBean ? this.jobBean.jobId : 0;
  }

  //聊天tab创建之后获取未读数
  getContactNoneReadMsgCount(filter?: (contact: ContactEntity) => boolean) {
    let count = 0
    const newResult = FilterManager.getResult(this.all.contactBeans, new ObtainNoneRTypeCountFilter())
    newResult.forEach(element => {
      if (filter?.(element) ?? true) {
        count += element.localInfo.noneReadCount
      }
    });
    return count
  }

  /**
   * 获取非自己的 其他联系人未读数量
   */
  public getOtherContactNoneReadMsgCount(friendId: number, friendSource: number): number {
    return this.getContactNoneReadMsgCountFromDb((contact) => {
      return !(contact.friendId == friendId && contact.friendSource == friendSource)
    })
  }

  //聊天tab未创建获取未读数
  getContactNoneReadMsgCountFromDb(filter?: (contact: ContactEntity) => boolean) {
    let count = 0
    const list = this.getAllContacts()
    const newResult = FilterManager.getResult(list, new ObtainNoneRTypeCountFilter())
    newResult.forEach(element => {
      let add: boolean = filter ? filter(element) : true
      if (add) {
        count += element.localInfo.noneReadCount
      }
    })
    this.unreadCount = count
    return count
  }

  //筛选职位赋值
  public setFilterJob(jobId: number) {
    let filterJob: ServerJobBean | undefined = undefined
    if (jobId > 0) {
      const allJobs = Kernel.getInstance().userService().boss().getJobList()
      for (let job of allJobs) {
        if (job.jobId == jobId) {
          filterJob = job
          break
        }
      }
    }
    this.jobBean = filterJob
  }

  public getJobName() {
    return this.jobBean ? this.jobBean.positionName : null;
  }

  public isSupportRecommendSort(groupType: number) {
    if (groupType == GroupType.NEW_GREETING && NotifyUtils.isNewContactCommon()) {
      return CommonConfigManager.getInstance().getGreetingRecSort() > 0
    }
    return false
  }

  public changeTopFriend(contact: ContactEntity, callback?: (success: boolean) => void) {
    const count = this.all.contactTops.length;
    if (count >= 60) {
      ToastUtils.showToast("您置顶的人数已达60人上限，请先取消置顶");
      return;
    }

    const isTop = !contact.isTop
    POST<SetTopContactResponse>(URLConfig.URL_FRIENDRELATION_ISTOP, {
      friendId: contact.friendId,
      isTop: isTop ? '1' : '0',
      friendSource: contact.friendSource,
      securityId: contact.securityId,
    }).then(async (resp: HttpResponse<SetTopContactResponse>) => {
      if (resp.zpData) {
        callback?.(true)
        contact.isTop = isTop
        await Kernel.getInstance()
          .getContactService()
          .updateTop(Kernel.getInstance().getUid(), Kernel.getInstance().getUserRole(), contact.friendId,
            contact.friendSource, isTop)
        if (isTop) {
          ToastUtils.showToast('置顶成功')
        } else {
          ToastUtils.showToast('取消置顶成功')
        }
      } else {
        callback?.(false)
      }
    }).catch((reason: ZPHttpError) => {
      callback?.(false)
      ToastUtils.showToast(reason.errorReason)
    })
  }

  public deleteContact(contact: ContactEntity, callback?: (success: boolean) => void) {
    POST<SuccessResponse>(URLConfig.URL_FRIEND_RELATION_DELETE, {
      friendId: contact.friendId,
      friendSource: contact.friendSource,
    }).then(async (resp: HttpResponse<SuccessResponse>) => {
      if (resp.zpData) {
        let account = Kernel.getInstance().getAccount()
        if (!account) {
          return
        }
        await Kernel.getInstance()
          .getContactService()
          .deleteContact(account.uid, account.identity, contact.friendId, contact.friendSource)
        const msgDao = await Kernel.getInstance().dbService().getMessageDao()
        msgDao?.deleteByFriendIdAndSource(contact.friendId, contact.friendSource)
        ToastUtils.showToast('删除成功')
        callback?.(true)
      }
    }).catch((reason: ZPHttpError) => {
      ToastUtils.showToast(reason.errorReason)
    })
  }

  public getUninterestedContact() {
    const filter = new GeekObtainUnInterestFilter()
    const result = FilterManager.getResult(this.allContact, filter);
    return result
  }

  public getUnfitContact() {
    const filter = new BossObtainRejectFilter()
    const result = FilterManager.getResult(this.allContact, filter);
    return result
  }

  public hasCollectContact() {
    for (const contactBean of this.allContact) {
      if (contactBean.isStar) {
        return true;
      }
    }
    return false
  }

  public getCollectContact() {
    const filter = new BossObtainStarFilter()
    const result = FilterManager.getResult(this.allContact, filter);
    return result
  }

  public async getUninterestedContactFromDb(): Promise<ContactEntity[]> {
    const allContacts = F2ContactModeManager.getAllContact()
    const filter = new GeekObtainUnInterestFilter()
    const result = FilterManager.getResult(allContacts, filter);
    return ContactSortManager.executeDefaultSort(result)
  }

  public async getUnfitContactFromDb(): Promise<ContactEntity[]> {
    const allContacts = F2ContactModeManager.getAllContact()
    const filter = new BossObtainRejectFilter()
    const result = FilterManager.getResult(allContacts, filter);
    return ContactSortManager.executeDefaultSort(result)
  }

  getRecommendContactSecurityIds() {
    const contactBeans = this.getContactGroupData(GroupType.NEW_GREETING).contactBeans
    ContactSortManager.executeDefaultSort(contactBeans);
    const count = Math.min(contactBeans.length, 100);
    if (count < 5) {
      return null;
    }
    let securityIdList = new Array<String>();
    for (let i = 0; i < count; i++) {
      const contactBean = contactBeans[i];
      if (contactBean != null) {
        if (DateUtils.isCurrentWeek(ContactClassManager.getShowTime(contactBean))) { //时间排序 出现一个超过7天的后续的就不用便利了
          continue;
        }
        if (TextUtils.isEmpty(contactBean.securityId)) {
          continue;
        }
        securityIdList.push(contactBean.securityId)
      }
    }
    if (securityIdList.length < 5) {
      return null;
    }
    return securityIdList.join(',')
  }

  /**
   * BOSS身份
   *
   * @return
   */
  private getGeekRecommendContactSecurityIds() {
    const contactBeans = this.getContactGroupData(GroupType.NEW_GREETING).contactBeans
    const count = LList.getCount(contactBeans);
    if (count < 5) {
      return null;
    }
    ContactSortManager.executeDefaultSort(contactBeans);
    const mapList = new Array<Record<string, Object>>();
    for (let i = 0; i < count; i++) {
      const contactBean = contactBeans[i];
      if (contactBean) {
        if (DateUtils.isSameWeek(ContactClassManager.getShowTime(contactBean))) { //时间排序 出现一个超过7天的后续的就不用便利了
          continue;
        }
        if (TextUtils.isEmpty(contactBean.securityId)) {
          continue;
        }
        let jsonObject: Record<string, Object> = {
          "securityId": contactBean.securityId,
          "unread": ContactClassManager.getUnreadCount(contactBean) > 0
        }
        mapList.push(jsonObject);
        if (mapList.length >= 100) { // 限制100个大小
          break;
        }
      }
    }
    if (mapList.length < 5) {
      return null;
    }
    TLog.debug(TAG, "RecommendContactSecurityId = %s", mapList.length);
    if (mapList.length > 1) {
      return mapList;
    }
    return null;
  }

  refreshSortType(groupType: number, sortType: number, callback: () => void) {
    const contactGroupData = this.getContactGroupData(groupType)
    if (contactGroupData) {
      contactGroupData.sortType = sortType
      if (sortType == ContactGroupBean.RECOMMEND_SORT_TYPE) {
        const geekRecommendContactSecurityIds = this.getGeekRecommendContactSecurityIds()
        if (LList.isEmpty(geekRecommendContactSecurityIds)) { //展示上一次的排序结果
          this.recommendSortData(contactGroupData);
          this.refreshSortTypeGroupData(contactGroupData, callback);
          return;
        }
        if(geekRecommendContactSecurityIds){
          let jsonObject: Record<string, Object> = {
            "geekList": geekRecommendContactSecurityIds
          }
          POST<GreetSortListResponse>(ChatUrlConfig.URL_ZPRELATION_BOSS_FRIEND_GREET_SORT_LIST, {
            scene: 1,
            geekInfos: JSON.stringify(jsonObject),
          }).then((resp: HttpResponse<GreetSortListResponse>) => {
            if (resp.zpData) {
              contactGroupData.recommend_sort_data = resp.zpData.sortedList ?? [];
              contactGroupData.sortTime = new Date().getTime();
              this.recommendSortData(contactGroupData);
              this.refreshSortTypeGroupData(contactGroupData, callback);
            }
          }).catch((reason: ZPHttpError) => {
            this.recommendSortData(contactGroupData);
            this.refreshSortTypeGroupData(contactGroupData, callback);
          })
        }


      } else {
        this.defaultSortData(contactGroupData.contactBeans);
        this.refreshSortTypeGroupData(contactGroupData, callback);
      }
    }
  }

  private defaultSortData(sourceList: Array<ContactEntity>) {
    //排序,按照"置顶"和"时间"俩个维度排序
    ContactSortManager.executeSortByTopStatusAndTime(sourceList);
  }

  private async refreshSortTypeGroupData(contactGroupData: ContactGroupBean, callback: () => void) {
    const contactBeans = contactGroupData.contactBeans;
    contactGroupData.clear();
    contactGroupData.sortSortPosition();
    if (contactBeans != null) {
      for (let contactBean of contactBeans) {
        contactGroupData.addContact(contactBean);
      }
    }
    callback()
  }

  private recommendSortData(contactGroupData: ContactGroupBean) {
    contactGroupData.sortSortPosition();
    if (contactGroupData.sortType == ContactGroupBean.RECOMMEND_SORT_TYPE) {
      //重新时间排序
      ContactSortManager.executeDefaultSort(contactGroupData.contactBeans);
      if (contactGroupData.sortTime > 0 && contactGroupData.recommend_sort_data != null &&
        contactGroupData.contactBeans != null) {
        //如果用户删除了数据， 更新缓存内容
        this.checkTempContactData(contactGroupData);
        let contactBeans = new Array<ContactEntity>();
        //第一部分：获取新增数据
        const recommendSortNewAddSourceData = contactGroupData.getRecommendSortNewAddSourceData();
        if (recommendSortNewAddSourceData) {
          if (CheckUtils.isNotEmptyArr(recommendSortNewAddSourceData)) {
            contactBeans.push(...recommendSortNewAddSourceData);
          }
        }

        //第二部分：获取推荐排序
        //获取排序后的列表
        const sortContact = contactGroupData.getRecommendSortSourceData();
        if (sortContact && !LList.isEmpty(sortContact)) {
          //去掉重复数据
          const sortContactIds = new Set(sortContact.map(bean => bean.friendId));
          const newSortContact = contactBeans.filter(item => !sortContactIds.has(item.friendId));
          newSortContact.push(...sortContact)
          contactBeans = newSortContact
        }

        if (!LList.isEmpty(contactBeans)) {

          //第三部：移除重新排的数据，剩下不排数据
          // 创建一个set来快速查找sortBeans中的元素
          const sortBeanSet = new Set(contactBeans.map(bean => bean.friendId));
          const newContacts = contactGroupData.contactBeans.filter(bean =>!sortBeanSet.has(bean.friendId));
          newContacts.push(...contactBeans)
          //第四部分：将已排序和新增的数据放入顶部
          contactGroupData.contactBeans = newContacts
        }
      }
    }
    return contactGroupData;
  }

  private checkTempContactData(contactGroupData: ContactGroupBean) {
    if (contactGroupData.temp_contactBeans == null || contactGroupData.temp_contactBeans.length == 0 ||
      contactGroupData.contactBeans == null) {
      return;
    }
    const contactTempBeans = new Array<ContactEntity>();
    for (let contactBean of contactGroupData.contactBeans) {
      const contactBeanTemp = contactGroupData.temp_contactBeans.get(this.getKey(contactBean));
      if (contactBeanTemp != null) {
        contactTempBeans.push(contactBeanTemp);
      }
    }
    contactGroupData.recordHistorySourceData(contactTempBeans);
  }

  getKey(contactBean: ContactEntity) {
    if (contactBean == null) {
      return null;
    }
    return contactBean.friendId + "_" + contactBean.friendSource;
  }

  public getNewContactRecommendSort(groupType: number) {
    const contactGroupData = this.getContactGroupData(groupType);

    if (contactGroupData) {
      if (contactGroupData.sortType > 0) {
        return contactGroupData.sortType;
      } else {
        return ContactGroupBean.RECOMMEND_SORT_TYPE;
      }
    }
    // if (this.isSupportRecommendSort(groupType)) {
    //   const contactGroupData = this.getContactGroupData(groupType);
    //   // TODO: tip
    //   // if (contactGroupData == null) {
    //   //   return CommonConfigManager.getInstance().getGreetingRecSort() == ContactGroupBean.RECOMMEND_SORT_TYPE;
    //   // }
    //   if(contactGroupData){
    //     return contactGroupData.sortType;
    //   }
    // }
    return ContactGroupBean.RECOMMEND_SORT_TYPE;
  }

  public getAllContactData() {
    return this.groupMap.get(GroupType.ALL);
  }

  async updateF2InterviewDesc(bean: ContactEntity) {
    const localField = bean.localInfo?.getLocalField()
    if (localField && !localField.hasJumpToChat) {
      localField.hasJumpToChat = true;
      await Kernel.getInstance()
        .getContactService()
        .upsert(Kernel.getInstance().getUid(), Kernel.getInstance().getUserRole(), bean)
    }

  }

  //同步未读数
  public async syncRead(callback?: (success: boolean) => void) {
    const groupContact = this.getContactGroupData(GroupType.ALL)
    const zpFriendIds = new Array<string>()
    const dzFriendIds = new Array<string>()
    if (groupContact) {
      Logger.debug(TAG, "syncRead start", DateUtils.getCurrentDateTime())
      const all = groupContact.contactBeans
      for (let c of all) {
        /*只处理有未读消息的联系人*/
        if (c.localInfo.noneReadCount <= 0) {
          continue;
        }
        if (c.friendSource == FriendSource.BOSS) {
          zpFriendIds.push(c.friendId.toString());
        } else {
          dzFriendIds.push(c.friendId.toString());
        }
      }
      Logger.debug(TAG, "syncRead end", DateUtils.getCurrentDateTime())
    }
    const zpFriendIdStr = zpFriendIds.join(',')
    const dzFriendIdsStr = dzFriendIds.join(',')
    if (TextUtils.isEmpty(zpFriendIdStr) && TextUtils.isEmpty(dzFriendIdsStr)) {
      setTimeout(() => {
        callback?.(false)
        Logger.debug(TAG, "syncRead end", DateUtils.getCurrentDateTime())
      }, 1000)
      return
    }

    POST<MessageReadResponse>(URLConfig.URL_MESSAGE_SYNC_READV2, {
      zpFriendIds: zpFriendIdStr,
      dzFriendIdsStr: dzFriendIdsStr
    }).then((resp) => {
      if (resp.zpData) {
        //todo strong
        // this.handleInChildThread(resp.zpData)
        setTimeout(() => {
          callback?.(true)
        }, 1000)
      }

    }).catch((reason: ZPHttpError) => {
      ToastUtils.showToast(reason.errorReason)
      callback?.(false)
    })
  }

  private async handleInChildThread(resp: MessageReadResponse) {
    let updateCount = 0;
    let dbCostTotalTime = 0;
    let isNoneReadChangeWhenQueryFromDb = false;
    for (let item of resp.syncList) {
      const contactDao = await Kernel.getInstance().dbService().getContactDao()
      if (contactDao) {
        const contact = await contactDao.getByFriendIdAndSource(item.userId, FriendSource.BOSS)
        if (contact) {
          let tempNoneReadCount = contact.localInfo.noneReadCount
          const startTime = new Date().getTime()
          //根据服务器 已读的 ID计算客户端 未读数量是多少
          const calculateNoneRead = await this.getItemNoneReadCount(item);
          const costTime = new Date().getTime() - startTime;
          if (tempNoneReadCount != contact.localInfo.noneReadCount) {
            isNoneReadChangeWhenQueryFromDb = true;
            //计算过程中，好友未读消息发生了变化，埋点上报
            Logger.info(TAG, "contact noneReadCount changed when query from db " +
              "preNoneReadCount= %d , nowNoneReadCount=%d , friendId= %d",
              tempNoneReadCount, contact.localInfo.noneReadCount, contact.friendId);
          }
          dbCostTotalTime += costTime;
          if (contact.localInfo.noneReadCount != calculateNoneRead) {
            updateCount++;

            // addTLogInfo(result, contact, calculateNoneRead);

            contact.localInfo.noneReadCount = calculateNoneRead;
            //保存到数据库
            await Kernel.getInstance()
              .getContactService()
              .upsert(Kernel.getInstance().getUid(), Kernel.getInstance().getUserRole(), contact)
          }
        }
      }
    }
    // TODO: apm上报
  }

  private async getItemNoneReadCount(itemBean: MessageReadBean) {
    const fromUId = itemBean.userId;
    const userSource = itemBean.userSource;
    const toUserId = Kernel.getInstance().getUid();
    let msgDao = await Kernel.getInstance().dbService().getMessageDao()
    let noneReadCount = 0;
    if (msgDao) {
      const chatList = await msgDao.queryCountByMsgIdUpper(itemBean.messageId, fromUId);

      for (let chatBean of chatList) {
        //撤回消息 不计数
        if (chatBean.status == 4 && itemBean.userId <= 1000) {
          continue;
        }
        //已读消息不计数
        if (chatBean.status == 2) {
          continue;
        }
        //不计数
        if (chatBean.badged == 1) {
          continue;
        }
        //系统消息
        if (chatBean.msgType == 4) {
          continue;
        }
        noneReadCount++;
      }
    }

    return noneReadCount;
  }

  //新招呼排序Tab
  public setContactHelloDataSource(group: ContactGroupBean, contactBeans: Array<ContactEntity>) {
    if (group.newHelloSortContact) {
      contactBeans.unshift(group.newHelloSortContact)
    }
  }

  //置顶数据
  public setContactTopDataSource(group: ContactGroupBean, contactBeans: Array<ContactEntity>) {
    if (CheckUtils.isNotEmptyArr(group.contactTops) && group.sortType != ContactGroupBean.RECOMMEND_SORT_TYPE) {
      if (group.topExpandContactDivider) {
        if (group.contactTops.length <= 4) {
          contactBeans.push(...group.contactTops)
        } else {
          if (this.isTopExpandState) {
            contactBeans.push(...group.contactTops)
          } else {
            contactBeans.push(...group.contactUnReadTops)
          }
        }
        contactBeans.push(group.topExpandContactDivider)
      }

      const contactTops: Array<ContactEntity> = group.contactTops
      // 过滤掉匹配条件的元素
      const exceptTopContacts = group.contactBeans.filter(
        (contact) =>
        !contactTops.some(
          (removeItem) =>
          removeItem.friendId === contact.friendId &&
            removeItem.friendSource === contact.friendSource
        )
      )
      return exceptTopContacts
    } else {
      return group.contactBeans
    }
  }
  //7天内无未读消息
  isNoneReadMsgTimeMillisOutWeek() {
    const singleContact = this.getAllContacts()
    const filter = new ObtainNoneReadFilter()
    const result = FilterManager.getResult(singleContact, filter)
    ContactSortManager.executeDefaultSort(result);
    const lastContactBean = result[0];
    if (lastContactBean) {
      //一周前的时间戳
      const preWeekTimeMillis = new Date().getTime() - 1000 * 60 * 60 * 24 * 7;
      if (lastContactBean.localInfo.messageField.chatTime > 0
        && lastContactBean.localInfo.messageField.chatTime < preWeekTimeMillis) {
        return true;
      } else {
        return false;
      }
    }
    return true;
  }
  //获得单聊未读联系人的集合
  public getSingleNoneReadContactList() {
    const filter = new ObtainNoneReadFilter()
    const result = FilterManager.getResult(this.getAllContacts(), filter)
    return result;
  }

  /**
   * n 天内无未读消息
   *
   * @return
   */
  public isNoneReadMsgTimeMillisOutDays(n: number) {
    const singleContact = this.getAllContacts();
    const filter = new ObtainNoneReadFilter()
    const result = FilterManager.getResult(singleContact, filter)
    ContactSortManager.executeDefaultSort(result);
    const lastContactBean = result[0];
    if (lastContactBean) {
      const preWeekTimeMillis = new Date().getTime() - 1000 * 60 * 60 * 24 * n;
      if (lastContactBean.localInfo.messageField.chatTime > 0
        && lastContactBean.localInfo.messageField.chatTime < preWeekTimeMillis) {
        return true;
      } else {
        return false;
      }
    }
    return true;
  }

  //获得最近一个未读消息的时间
  public getLastNoneReadMsgTime() {
    const singleContact = this.getAllContacts();
    const filter = new ObtainNoneReadFilter()
    const result = FilterManager.getResult(singleContact, filter)
    if (!CheckUtils.isEmptyArr(result)) {
      ContactSortManager.executeDefaultSort(result);
      const lastContactBean = result[0];
      if (lastContactBean) {
        return lastContactBean.localInfo.messageField.chatTime;
      }
    }
    return 0;
  }


  getAllQuickHandleContact() {
    const result = new Array<ContactEntity>();
    const contactList = this.getAllContacts();
    for (const contactBean of contactList) {

      if (ContactClassManager.haveSentMsgToFriend(contactBean)) continue;

      if (ContactClassManager.isCanFastHandler(contactBean)) {
        result.push(contactBean);
      }
    }
    return result;
  }

  public isFilterOptionOn(): boolean {
    return this.isFilterOption;
  }

  public getSelectFilter(): F2ContactFilterType {
    return this.selectFilter
  }

  setFilter(filter: F2ContactFilterType) {
    this.selectFilter = filter
  }

  public hasFilter() {
    return this.selectFilter != F2ContactFilterType.None
  }

  public isSupportItemSourceFilterOn() {
    return this.isSupportItemSourceFilter;
  }

  public hasFastHandleContactBool() {
    return this.hasFastHandleContact;
  }

  //获得单聊 30天内Vip筛选不符合条件的人数量
  public getUnfitCountVip() {
    return LList.getCount(this.getFilterContact());
  }

  /**
   * 联系人 30天内Vip筛选不符合条件的牛人或者boss列表
   * todo 测试 GeekFilterContactActivity
   *
   * @return
   */
  public getFilterContact() {
    const filter = new ObtainFilteredUserFilter()
    const singleContact = this.getSingleContact(true)
    const result = FilterManager.getResult(singleContact, filter);
    return result
  }

  //联系人 30天内Vip筛选不符合条件的牛人时间
  public getUnfitTimeVip() {
    let unfitTime = 0;
    const filterContact = this.getFilterContact();
    for (const contactBean of filterContact) {
      if (contactBean.localInfo.messageField.chatTime > unfitTime) {
        unfitTime = contactBean.localInfo.messageField.chatTime
      }
    }
    return unfitTime;
  }

  //联系人 30天内Vip筛选不符合条件的牛人红点
  public getUnfitRedPointVip() {
    let noneReadCount = 0;
    const filter = new ObtainFilteredUserFilter()
    const singleContact = this.getSingleContact(true)
    const result = FilterManager.getResult(singleContact, filter);
    for (const contactBean of result) {
      noneReadCount += contactBean.localInfo.noneReadCount;
    }
    return noneReadCount
  }

  /**
   * 获得单聊联系人，是否包含 不合适，设置filter等的数据
   *
   * @param include
   * @return
   */
  public getSingleContact(include: boolean): ContactEntity[] {
    if (include) {
      return F2ContactModeManager.getSingleContact();
    } else {
      return this.allContact
    }
  }

  /**
   * 是否系统消息,小于1000的消息或本地虚拟的联系人 （测试-done ，消息列表点击头像,长按等）
   *
   * @param friendId
   * @return
   */
  public isSystemContact(friendId: number) {
    const localContact = VirtualContactManager.getInstance().isVirtualContact(friendId);
    Logger.debug("isSystemContact: " + friendId)
    if (localContact) {
      return true;
    }

    const serverSysContact = F2ContactModeManager.isServerSysContact(friendId);
    if (serverSysContact) {
      return true;
    }

    return false;
  }

  //联系人 30天内Vip筛选不符合条件的牛人或者boss列表
  public getUnfitGeekList(pageNum: number) {
    const unfitPageList = new Array<ContactEntity>();
    const filterContact = this.getFilterContact();
    const result = ContactSortManager.executeDefaultSort(filterContact);
    if (!LList.isEmpty(result)) {
      for (let i = pageNum * 15; i < (pageNum + 1) * 15; i++) {
        if (LList.getElement(result, i) == null) {
          continue;
        }
        unfitPageList.push(result[i]);
      }
    }
    return unfitPageList;
  }

  ////联系人 30天内Vip筛选不符合条件的牛人
  public getUnfitRedPointVip2() {
    const filter = new ObtainFilteredUserFilter()
    const noneFilter = new ObtainNoneReadFilter()
    const singleContact = this.getSingleContact(true)
    const result = FilterManager.getResult(singleContact, filter, noneFilter);
    return result;
  }


  /**
   * 清空未读
   */
  public async clearNoneRead(contact: ContactEntity, sendMsg: boolean = true) {
    if (contact) {
      if (contact.localInfo.noneReadCount > 0) {
        contact.localInfo.noneReadCount = 0
        const contactDao = await Kernel.getInstance().dbService().getContactDao()
        if (contactDao) {
          // 该联系人的未读消息数清零 & 如果是最后一条消息则置为已读
          await contactDao.setAllMessagesRead(contact.friendId, contact.friendSource, 0)
        }
        if(sendMsg){
          this.sendReadMessage(contact)
        }
      }
    }
  }

  public async sendReadMessage(contact: ContactEntity, contactSync = true) {
    if (contact) {
      const messageDao = await Kernel.getInstance().dbService().getMessageDao()
      if (messageDao) {
        const unreadMid: number = await messageDao.queryMaxMidBySender(contact.friendId, contact.friendId)
        const readMsg = new ReadMessage()
        readMsg.create(contact.friendId, contact.friendSource, unreadMid, Date.now(), false)
        readMsg.contactSync = contactSync
        let callback = new DefaultCallback()
        await SenderHandler.sendReadMessage(readMsg, callback)
      }
    }
  }

  public async sendMessageReaderMessage(contacts: Array<ContactEntity>, contactSync = true) {
    if (contacts) {
      const bossIds= new Array<number>()
      const dianZhangIds= new Array<number>()
      for(const contact of contacts){
        if (contact.friendSource == ContactPageData.FROM_BOSS) {
          bossIds.push(contact.friendId);
        } else {
          // 店长联系人
          dianZhangIds.push(contact.friendId);
        }
      }
      //没有消息发送
      if (LList.isEmpty(bossIds) && LList.isEmpty(dianZhangIds)) return
      await ReaderHandler.sendMessageReaderMessage(bossIds, ContactPageData.FROM_BOSS)
      await ReaderHandler.sendMessageReaderMessage(dianZhangIds, ContactPageData.FROM_DIAN_ZHANG)
    }
  }


  //todo c端联系人筛选底部弹窗需求在加
  private createMultiFilterData() {
    if (!this.multiFilter) {
      if (this.isGeekContactFilter()) {
        this.multiFilter = new GeekFilterData();
      }
      // else if (this.isBossContactFilter()) {
      //   this.multiFilter = new BossFilterData();
      // }
    }
    return this.multiFilter;
  }

  public isGeekContactFilter(): boolean {
    return Kernel.getInstance().isGeekRole()
      // && DataStarGray.getInstance().isGeekContactFilter();
  }

  public getGeekFilterData() {
    if (this.multiFilter instanceof GeekFilterData) {
      return this.multiFilter as GeekFilterData;
    }
    return undefined;
  }

  public getBossFilterData() {
    if (this.multiFilter instanceof BossFilterData) {
      return this.multiFilter as BossFilterData;
    }
    return null;
  }

  /**
   * 一周之前分割线
   *
   * @param data
   */

  //新招呼排序Tab

  public setContactWeekAgeDataSource(weekAgeContactDivider?: ContactEntity, data?: Array<ContactEntity>) {
    if (weekAgeContactDivider && data) {
      let index = -1;
      for (let i = 0; i < data.length; i++) {
        const contactBean = data[i];
        if (ContactUtils.isWeekAgeGray(contactBean)) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        if(!data.find(item => item.name === '一周之前消息')){
          CommonUtil.addElement(data, weekAgeContactDivider, index)
        }
      }
    }
  }


  /**
   * 新招呼选择了推荐排序
   * @param groupType
   * @return
   */
  public isNewGreetingContactRecommendSort(groupType: number) {
    if (this.isSupportNewGreetingRecommendSort(groupType)) {
      const contactGroupData = this.getContactGroupData(groupType);
      if (contactGroupData) {
        return contactGroupData.sortType == ContactGroupBean.RECOMMEND_SORT_TYPE;
      } else {
        return CommonConfigManager.getInstance().getGreetingRecSort() == ContactGroupBean.RECOMMEND_SORT_TYPE;
      }
    }
    return false;
  }

  public async updateContactListLastMsgStatus(contacts: Array<ContactEntity>, success: boolean){
    try {
      const contactDao = await Kernel.getInstance().dbService().getContactDao()
      if (contactDao) {
        for(const c of contacts){
          c.localInfo.messageField.msgState = success ? MessageStatus.Success : MessageStatus.Failure
        }
        await contactDao.upsertArr(contacts)

        const allContact = Kernel.getInstance().getContactService().getF2ContactManager().allContact
        contacts.forEach((item) => {
          const c = allContact.find(it => (it.friendId === item.friendId && it.friendSource === item.friendSource))
          if (c?.localInfo) {
            c.localInfo.messageField.msgState = success ? MessageStatus.Success : MessageStatus.Failure
          }
        })
        getContext()
          .getApplicationContext()
          .eventHub
          .emit(ZPEvents.Contact.LoadContact)
      }
    } catch (e) {
    }
  }

  public async updateContactListUnfit(contacts: Array<ContactEntity>, isRejectUser: boolean, rejectDesc: string, isStar: boolean ) {
    try {
      const contactDao = await Kernel.getInstance().dbService().getContactDao()
      if (contactDao) {
        for(const c of contacts){
          c.localInfo.noneReadCount = 0
          c.isRejectUser = isRejectUser
          c.rejectDesc = rejectDesc
          c.isStar = isStar
        }
        await contactDao.upsertArr(contacts)
        const allContact = Kernel.getInstance().getContactService().getF2ContactManager().allContact
        contacts.forEach((item) => {
          const c = allContact.find(it => (it.friendId === item.friendId && it.friendSource === item.friendSource))
          if (c?.localInfo) {
            c.localInfo.noneReadCount = 0
            c.isRejectUser = isRejectUser
            c.rejectDesc = rejectDesc
            c.isStar = isStar
          }
        })
        getContext()
          .getApplicationContext()
          .eventHub
          .emit(ZPEvents.Contact.LoadContact)
      }
    } catch (e) {
    }

  }


}

export enum F2FriendType {
  Single = 1,
  Group = 2,
  System = 3,
  WeekAge = -7,
  TopDivider = -8,
  NewHelloSort = -9,
  NonFoldTopDivider = -10,
}