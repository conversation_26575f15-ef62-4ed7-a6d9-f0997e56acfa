import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactDao } from '../db/dao/ContactDao';
import { OnlyContactDao } from '../db/dao/OnlyContactDao';
import { Kernel } from '../../Kernel';
import { Logger } from 'lib_zputils/Index';
import { F2FriendType } from '../f2/F2ContactManager';
import { PagingSource, LoadParams, LoadResult, LoadResultPage, PagingState, LoadDirection, Page} from './PagingSource';
import { ContactDataItemIdentifier } from './ContactDataItemIdentifier';
import { ContactUtils } from '../ContactUtils';

const TAG = 'OnlyContactPagingSource';

/**
 * 分页键，用于数据库查询定位
 * - sortValue: 主要排序依据（如时间戳）
 * - id: 次要排序依据，用于处理sortValue相同时的排序稳定性
 */
export interface PagingKey {
  sortValue: number;
  id?: number;
}

/**
 * 仅沟通分组的分页数据源实现。
 * 继承自通用的 PagingSource，负责从数据库加载联系人数据。
 */
export class OnlyContactPagingSource extends PagingSource<PagingKey, ContactEntity> {
  private contactDao: ContactDao | null = null;
  private onlyContactDao: OnlyContactDao | null = null;
  private itemIdentifier: ContactDataItemIdentifier;

  // WeekAge分割线相关状态
  private weekAgeDividerInserted: boolean = false;


  // 🐌 网络延迟模拟配置
  private static ENABLE_SLOW_SIMULATION = false; // 是否启用慢网络模拟
  private static SLOW_DELAY_MS = 2000; // 延迟时间（毫秒）
  private static RANDOM_DELAY = true; // 是否使用随机延迟

  constructor() {
    super();
    this.itemIdentifier = new ContactDataItemIdentifier();
    // 构造函数中不适合异步操作，将DAO的初始化延迟到第一次load时
  }

  /**
   * 🐌 模拟网络延迟
   */
  private async simulateNetworkDelay(): Promise<void> {
    if (!OnlyContactPagingSource.ENABLE_SLOW_SIMULATION) {
      return;
    }

    let delay = OnlyContactPagingSource.SLOW_DELAY_MS;

    if (OnlyContactPagingSource.RANDOM_DELAY) {
      // 随机延迟：50% - 150% 的基础延迟时间
      const randomFactor = 0.5 + Math.random();
      delay = Math.floor(delay * randomFactor);
    }

    Logger.debug(TAG, `🐌 模拟网络延迟: ${delay}ms`);

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
      }, delay);
    });
  }







  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao || !this.onlyContactDao) {
      this.contactDao = await Kernel.getInstance().dbService().getContactDao();
      if (!this.contactDao) {
        throw new Error('ContactDao or BaseDao initialization failed');
      }

      this.onlyContactDao = new OnlyContactDao(this.contactDao);
      Logger.debug(TAG, 'ContactDao and OnlyContactDao initialized successfully');
    }
  }

  /**
   * 核心加载方法。根据提供的分页键从数据库加载数据。
   */
  async load(params: LoadParams<PagingKey>): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      await this.ensureDaoInitialized();
      Logger.debug(TAG, `🚀 开始加载数据: ${JSON.stringify(params)}`);

      // 🐌 模拟网络延迟
      await this.simulateNetworkDelay();

      const key = params.key;
      const loadSize = params.loadSize;
      const direction = params.direction;

      let sql: string;
      let sqlParams: (string | number | boolean | null)[];

      // 使用LIMIT + 1技巧来准确判断是否还有更多数据
      const querySize = loadSize + 1;

      // 根据加载方向选择不同的SQL构建方法
      switch (direction) {
        case LoadDirection.INIT:
          // 🔥 INIT时重置状态并加载混合数据（置顶 + 分割线 + 普通联系人）
          this.weekAgeDividerInserted = false;
          return await this.loadMixedData(querySize);
        case LoadDirection.REFRESH:
          if (!key) {
            // 🔥 REFRESH无key时重置状态并加载混合数据
            this.weekAgeDividerInserted = false;
            return await this.loadMixedData(querySize);
          }
          sql = this.onlyContactDao!.buildOnlyContactAppendSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];;
          break;
        case LoadDirection.APPEND:
          if (!key) throw new Error('Key is required for APPEND load');
          sql = this.onlyContactDao!.buildOnlyContactAppendSql(this.filterType, this.jobId);
          // 正确的参数: sortValue, sortValue, id, querySize (loadSize + 1)
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.PREPEND:
           if (!key) throw new Error('Key is required for PREPEND load');

           sql = this.onlyContactDao!.buildOnlyContactPrependSql(this.filterType, this.jobId);
           // 正确的参数: sortValue, sortValue, id, querySize (loadSize + 1)
           sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
           break;
        default:
          throw new Error(`Unsupported load direction: ${direction}`);
      }

      Logger.debug(TAG, `Executing SQL: ${sql} with params: ${JSON.stringify(sqlParams)}`);

      // 3. 执行查询
      let contacts = await this.contactDao?.rawQueryContacts(sql, sqlParams);
      if (!contacts) {
        contacts = [];
      }

      Logger.debug(TAG, `raw query returned ${contacts.length} contacts (requested ${querySize})`);

      // 4. 使用LIMIT + 1技巧判断是否还有更多数据
      let hasMoreData = false;
      if (contacts.length > loadSize) {
        // 如果返回的数据超过请求的loadSize，说明还有更多数据
        hasMoreData = true;
        // 移除多余的一条数据，保持返回数据量为loadSize
        contacts = contacts.slice(0, loadSize);
        Logger.debug(TAG, `has more data, trimmed to ${contacts.length} contacts`);
      } else {
        // 返回的数据等于或少于loadSize，说明已经到达边界
        hasMoreData = false;
        Logger.debug(TAG, `reached end of pagination, returned ${contacts.length} contacts`);
      }

      // PREPEND查询的特殊处理：
      // 数据库返回的是ASC排序（从最接近当前key的数据开始）
      // 但UI需要DESC排序显示，所以需要reverse
      // 这样可以确保新加载的数据能正确插入到列表顶部
      if (direction === LoadDirection.PREPEND) {
        if (!hasMoreData) {
          return await this.loadMixedData(querySize);
        }
        contacts.reverse();
      }

      // 🔥 新增：在APPEND加载时检查是否需要插入WeekAge分割线
      if (direction === LoadDirection.APPEND && !this.weekAgeDividerInserted) {
        contacts = await this.insertWeekAgeDividerIfNeeded(contacts);
      }



      Logger.debug(TAG, `final result: ${contacts.length} contacts, hasMore=${hasMoreData}`);

      const prevKey = this.getPrevKey(contacts, direction, hasMoreData);
      const nextKey = this.getNextKey(contacts, direction, hasMoreData);

      const page: LoadResultPage<PagingKey, ContactEntity> = {
        type: 'PAGE',
        data: contacts,
        prevKey: prevKey,
        nextKey: nextKey,
      };
      return page;
    } catch (e) {
      Logger.error(TAG, `load failed: ${e}`);
      return {
        type: 'ERROR',
        error: e instanceof Error ? e : new Error('Unknown error during data loading')
      };
    }
  }

  /**
   * 确定上一页的键。
   * 根据加载方向和数据边界状态正确生成prevKey：
   * - REFRESH/APPEND: 取第一项（时间最新的）
   * - PREPEND: 取最后一项（reverse后的最后一项，即原始时间最新的）
   */
  private getPrevKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
     if (items.length === 0) {
       return null;
     }

     // 对于PREPEND方向，如果没有更多数据，说明已经到达最新数据的边界
     if (direction === LoadDirection.PREPEND && !hasMoreData) {
       Logger.debug(TAG, 'PREPEND reached beginning, no more newer data');
       return null;
     }

     if (direction === LoadDirection.INIT) {
       return null;
     }

     // 对于其他方向，正常生成prevKey
     let keyItem: ContactEntity;
     keyItem = items[0];

     return {
       sortValue: this.calculateSortValue(keyItem),
       id: keyItem.id
     };
  }

  /**
   * 确定下一页的键。
   * 根据加载方向和数据边界状态正确生成nextKey：
   * - REFRESH/APPEND: 取最后一项（时间最旧的）
   * - PREPEND: 取第一项（reverse后的第一项，即原始时间最旧的）
   */
  private getNextKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    // 使用LIMIT + 1技巧的结果来准确判断是否还有更多数据
    // 对于APPEND方向，如果没有更多数据，说明已经到达最旧数据的边界
    if ((direction === LoadDirection.APPEND || direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) && !hasMoreData) {
      Logger.debug(TAG, 'APPEND reached end, no more older data');
      return null;
    }

    // 对于其他情况，正常生成nextKey
    let keyItem: ContactEntity;
    keyItem = items[items.length - 1]; // 正常情况下的最后一项
    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  private calculateSortValue(contact: ContactEntity): number {
    // 🔥 简化：直接使用local_chatTime，不再需要置顶偏移量
    // 因为置顶和非置顶数据已经分离查询，在代码层面组合
    const chatTime = contact.localInfo?.messageField?.chatTime ?? 0;
    return chatTime;
  }

  /**
   * 创建PagingKey的便捷方法
   */
  private createPagingKey(contact: ContactEntity): PagingKey {
    return {
      sortValue: this.calculateSortValue(contact),
      id: contact.id
    };
  }

  /**
   * 检查PREPEND加载时是否需要包含置顶数据
   *
   * 场景分析：
   * 1. 页面被回收后，用户下拉刷新时走PREPEND方向
   * 2. 如果用户之前滚动到了置顶联系人下方，PREPEND应该能加载到置顶数据
   * 3. 如果用户之前就在置顶联系人上方，PREPEND不应该重复加载置顶数据
   */
  private async shouldIncludeTopDataInPrepend(key: PagingKey): Promise<boolean> {
    try {
      // 1. 优先使用缓存的置顶数据
      let topContacts = this.topContactsCache;

      if (topContacts.length === 0) {
        // 缓存为空，查询置顶联系人（应用筛选条件）
        const topSql = this.onlyContactDao!.buildOnlyContactTopSql(this.filterType, this.jobId);
        topContacts = await this.contactDao!.rawQueryContacts(topSql, []);

        if (topContacts.length === 0) {
          // 没有置顶联系人，不需要包含
          Logger.debug(TAG, 'shouldIncludeTopDataInPrepend: no top contacts found');
          return false;
        }
      }

      // 2. 获取最新的置顶联系人的时间
      const latestTopContact = topContacts[0]; // 已按时间降序排列
      const latestTopTime = latestTopContact.localInfo?.messageField?.chatTime ?? 0;

      // 3. 获取最旧的置顶联系人的时间
      const oldestTopContact = topContacts[topContacts.length - 1];
      const oldestTopTime = oldestTopContact.localInfo?.messageField?.chatTime ?? 0;

      // 4. 判断逻辑：
      // - 如果当前key的时间 < 最旧的置顶时间：用户在置顶区域上方，不需要包含置顶
      // - 如果当前key的时间 >= 最旧的置顶时间：用户在置顶区域内或下方，需要包含置顶
      const shouldInclude = key.sortValue >= oldestTopTime;

      Logger.debug(TAG, `shouldIncludeTopDataInPrepend: key.sortValue=${key.sortValue}, topTimeRange=[${oldestTopTime}, ${latestTopTime}], shouldInclude=${shouldInclude}`);

      return shouldInclude;

    } catch (e) {
      Logger.error(TAG, `shouldIncludeTopDataInPrepend failed: ${e}`);
      // 出错时保守处理，包含置顶数据以确保用户能看到
      return true;
    }
  }

  /**
   * 提供初始刷新时的键。
   * 优化版本：优先使用anchorItemId，避免遍历页面数据
   */
  getRefreshKey(state: PagingState<PagingKey, ContactEntity> | null): PagingKey | null {
    // 如果没有状态信息，从头开始
    if (!state) {
      return null;
    }

    // 策略1: 优先使用anchorItemId（基于锚点的优化）
    if (state.anchorItemId) {
      Logger.debug(TAG, `getRefreshKey: using anchor item ID ${state.anchorItemId} for optimized refresh`);

      // 找到锚点所在的page，使用该page的prevKey作为刷新起始点
      return this.createPagingKeyFromAnchorId(state.anchorItemId, state);
    }
    // 策略3: 从头开始
    Logger.debug(TAG, 'getRefreshKey: no anchor info available, starting from beginning');
    return null;
  }

  /**
   * 基于锚点ID创建PagingKey（优化方法）
   *
   * 核心思路：
   * 1. 用户滚动时，我们记录了当前可见的锚点项ID
   * 2. 数据失效需要刷新时，我们找到该锚点项所在的page
   * 3. 使用该page的prevKey作为刷新起始点
   * 4. 这样刷新后会从锚点附近开始加载，而不是从头开始
   *
   * 优势：
   * - 避免遍历所有页面数据 (O(1) vs O(页面数×页面大小))
   * - 刷新后用户能看到熟悉的内容，体验更好
   * - 充分利用已有的page结构和prevKey信息
   */
  private createPagingKeyFromAnchorId(anchorItemId: string, state: PagingState<PagingKey, ContactEntity>): PagingKey | null {
    try {
      // anchorItemId的格式是 "friendId_friendSource"，不是简单的数字
      // 直接使用字符串ID在pages中查找

      // 在pages中查找包含该锚点项的page
      const targetPage = this.findPageContainingAnchor(anchorItemId, state.pages);
      if (!targetPage) {
        Logger.warn(TAG, `anchor item ${anchorItemId} not found in any page`);
        return null;
      }

      // 使用该page的nextKey作为刷新起始点
      // 这样刷新后会从锚点附近开始加载，而不是从头开始
      const refreshKey = targetPage.nextKey;

      if (refreshKey) {
        Logger.debug(TAG, `found anchor item ${anchorItemId} in page, using nextKey: sortValue=${refreshKey.sortValue}, id=${refreshKey.id}`);
      } else {
        Logger.debug(TAG, `found anchor item ${anchorItemId} in first page, nextKey is null (will start from beginning)`);
      }

      return refreshKey;

    } catch (error) {
      Logger.error(TAG, `error creating paging key from anchor ID ${anchorItemId}: ${error}`);
      return null;
    }
  }

  /**
   * 在pages中查找包含指定锚点项的page
   * 使用ContactDataItemIdentifier的getId规则进行匹配
   * 简化版本：pages数组本身就是有序的
   */
  private findPageContainingAnchor(anchorItemId: string, pages: Array<Page<PagingKey, ContactEntity>>): Page<PagingKey, ContactEntity> | null {

    for (let i = 0; i < pages.length; i++) {
      for (let index = 0; index < pages[i].data.length; index++) {
        if (this.itemIdentifier.getId(pages[i].data[index]) === anchorItemId) {
          Logger.debug(TAG, `found anchor item ${anchorItemId} in page ${i} with ${pages[i].data.length} items`);
          if (i > 0) {
            return pages[i -1];
          }
        }
      }
    }

    Logger.debug(TAG, `anchor item ${anchorItemId} not found in ${pages.length} pages`);
    return null;
  }

  async getAllCount(): Promise<number> {
    try {
      await this.ensureDaoInitialized();
      return await  this.onlyContactDao!.queryOnlyContactAllCountSql();
    } catch (e) {
      Logger.error(TAG, `getAllCount failed: ${e}`);
      return 0;
    }
  }

  /**
   * 加载混合数据（置顶 + 分割线 + 普通联系人）
   * 用于INIT和无key的REFRESH场景
   */
  private async loadMixedData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 1. 加载置顶联系人（应用筛选条件）
      const topSql = this.onlyContactDao!.buildOnlyContactTopSql(this.filterType, this.jobId);
      const topContacts = await this.contactDao!.rawQueryContacts(topSql, []);
      Logger.debug(TAG, `loaded ${topContacts.length} top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 缓存置顶联系人
      this.topContactsCache = topContacts;

      // 2. 加载普通联系人（排除置顶，应用筛选条件）
      const nonTopSql = this.onlyContactDao!.buildOnlyContactNonTopSql(this.filterType, this.jobId);
      const nonTopContacts = await this.contactDao!.rawQueryContacts(nonTopSql, [querySize]);
      Logger.debug(TAG, `loaded ${nonTopContacts.length} non-top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 3. 构建混合数据
      const mixedData: ContactEntity[] = [];

      // 添加置顶联系人
      if (topContacts.length > 0) {
        if (topContacts.length <= 4) {
          // ≤4个置顶：显示所有置顶
          mixedData.push(...topContacts);
        } else {
          // >4个置顶：根据展开状态决定显示内容
          if (this.isTopExpanded) {
            mixedData.push(...topContacts);
          } else {
            // 只显示有未读的置顶联系人
            const unreadTopContacts = topContacts.filter(c => c.localInfo.noneReadCount > 0);
            mixedData.push(...unreadTopContacts);
          }
        }

        // 添加置顶分割线
        const divider = this.createTopDivider(topContacts.length);
        mixedData.push(divider);
      }

      // 添加普通联系人，并在适当位置插入WeekAge分割线
      const finalContacts = await this.insertWeekAgeDivider(nonTopContacts);
      mixedData.push(...finalContacts);

      Logger.debug(TAG, `mixed data: ${topContacts.length} top + dividers + ${nonTopContacts.length} normal = ${mixedData.length} total`);

      // 4. 构建分页键
      const prevKey = null;
      const nextKey = mixedData.length > 0 ? this.createPagingKey(mixedData[mixedData.length - 1]) : null;

      return {
        type: 'PAGE',
        data: mixedData,
        prevKey: prevKey,
        nextKey: nextKey
      };

    } catch (e) {
      Logger.error(TAG, `loadMixedData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 创建置顶分割线对象
   */
  private createTopDivider(topCount: number): ContactEntity {
    const divider = new ContactEntity();

    if (topCount > 4) {
      divider.friendId = F2FriendType.TopDivider;  // 可折叠分割线
    } else {
      divider.friendId = F2FriendType.NonFoldTopDivider;  // 简单分割线
    }

    divider.name = "折叠置顶聊天";
    divider.localInfo.friendDefaultAvatarIndex = topCount;

    // 计算未读置顶联系人数量
    const unreadTopCount = this.topContactsCache.filter(c => c.localInfo.noneReadCount > 0).length;
    divider.localInfo.noneReadCount = unreadTopCount;

    return divider;
  }

  /**
   * 在联系人列表中插入WeekAge分割线（用于初始加载）
   * 找到第一个一周前的联系人位置并插入分割线
   */
  private async insertWeekAgeDivider(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 找到第一个一周前的联系人位置
    let weekAgeIndex = -1;
    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i];
      if (ContactUtils.isWeekAgeGray(contact)) {
        weekAgeIndex = i;
        break;
      }
    }

    // 如果找到了一周前的联系人，插入分割线
    if (weekAgeIndex > -1) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, weekAgeIndex);
      result.splice(weekAgeIndex, 0, weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at index ${weekAgeIndex}`);
      return result;
    }

    return contacts;
  }

  /**
   * 在APPEND加载时检查是否需要插入WeekAge分割线
   * 这个方法处理分页加载过程中跨越一周边界的情况
   */
  private async insertWeekAgeDividerIfNeeded(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 检查是否有一周前的联系人
    let hasWeekAgeContact = false;
    for (const contact of contacts) {
      if (ContactUtils.isWeekAgeGray(contact)) {
        hasWeekAgeContact = true;
        break;
      }
    }

    // 如果有一周前的联系人，在第一个位置插入分割线
    // 因为这是APPEND加载，分割线应该在这批数据的开头
    if (hasWeekAgeContact) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, 0);
      result.unshift(weekAgeDivider); // 插入到开头
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at beginning during APPEND load`);
      return result;
    }

    return contacts;
  }

  /**
   * 创建WeekAge分割线对象
   */
  private async createWeekAgeDivider(contacts: ContactEntity[], insertIndex: number): Promise<ContactEntity> {
    const divider = new ContactEntity();
    divider.friendId = F2FriendType.WeekAge;
    divider.name = "一周之前消息";
    divider.localInfo.avatarIndex = insertIndex;

    // 🔥 使用SQL查询获取所有一周前联系人的准确未读数量
    try {
      await this.ensureDaoInitialized();
      const totalUnreadCount = await this.onlyContactDao!.queryOnlyContactWeekAgeUnreadCount(this.filterType, this.jobId);
      divider.localInfo.noneReadCount = totalUnreadCount;
      Logger.debug(TAG, `WeekAge divider unread count from SQL: ${totalUnreadCount}`);
    } catch (error) {
      Logger.error(TAG, `Failed to query WeekAge unread count: ${error}`);
      // 降级处理：使用当前可见数据计算
      let unreadCount = 0;
      for (let i = insertIndex; i < contacts.length; i++) {
        const contact = contacts[i];
        if (ContactUtils.isWeekAgeGray(contact) && contact.localInfo.noneReadCount > 0) {
          unreadCount += contact.localInfo.noneReadCount;
        }
      }
      divider.localInfo.noneReadCount = unreadCount;
    }

    return divider;
  }
}
