/**
 * @file PagingSource.ets
 * @brief Defines the core abstract components for the generic paging library.
 *
 * This file contains the fundamental building blocks for creating paginated lists,
 * including the PagingSource abstract class, data loading parameters, and result types.
 * It is designed to be data-agnostic, allowing it to be used for any data type.
 */

/**
 * 定义加载方向
 */
export enum LoadDirection {
  INIT = 'INIT',
  REFRESH = 'REFRESH',
  APPEND = 'APPEND',
  PREPEND = 'PREPEND'
}

/**
 * 加载状态类型
 */
export enum LoadStateType {
  NotLoading = 'NotLoading',
  Loading = 'Loading',
  Error = 'Error'
}

/**
 * 单一的加载状态
 * @template E 错误类型，默认为Error
 *
 * 注意：移除了endOfPaginationReached字段，因为智能删除会导致该状态失效
 * 边界判断应该基于当前数据动态计算，而不是存储全局状态
 */
export interface LoadState<E extends Error = Error> {
  type: LoadStateType;
  error?: E;
}

/**
 * 组合的加载状态，涵盖了刷新、前向和后向加载
 */
export interface LoadStates {
  refresh: LoadState;
  prepend: LoadState;
  append: LoadState;
}

/**
 * 分页库的配置选项
 */
export interface PagingConfig {
  pageSize: number;
  prefetchDistance: number;
  enablePlaceholders: boolean;
  initialLoadSize: number;
  maxSize: number;
}

/**
 * 代表一页加载的数据
 * @template Key 分页键类型
 * @template Value 数据项类型
 */
export interface Page<Key, Value> {
  data: Value[];
  prevKey: Key | null;
  nextKey: Key | null;
}

/**
 * Pager的内部状态，用于 getRefreshKey
 * @template Key 分页键的类型
 * @template Value 列表项数据的类型
 */
export interface PagingState<Key, Value> {
  pages: Page<Key, Value>[];
  anchorItemId?: string;  // 锚点项的唯一ID，用于优化刷新定位
  config: PagingConfig;
}

/**
 * `PagingSource.load()` 方法的加载参数
 * @template Key 分页键的类型 (e.g., number for page number, string for cursor)
 */
export interface LoadParams<Key> {
  key: Key | null;
  loadSize: number;
  direction: LoadDirection;
}

/**
 * `PagingSource.load()` 方法的成功加载结果
 * @template Key 分页键的类型
 * @template Value 列表项数据的类型
 */
export interface LoadResultPage<Key, Value> {
  type: 'PAGE';
  data: Value[];
  prevKey: Key | null;
  nextKey: Key | null;
  itemsBefore?: number;
  itemsAfter?: number;
}

/**
 * `PagingSource.load()` 方法的错误结果
 * @template E 错误类型
 */
export interface LoadResultError<E extends Error> {
  type: 'ERROR';
  error: E;
}

/**
 * `PagingSource.load()` 的联合结果类型
 */
export type LoadResult<Key, Value> = LoadResultPage<Key, Value> | LoadResultError<Error>;

/**
 * PagingSource 是分页库的核心。
 * 它是加载数据快照到 PagingData 流中的基类。
 *
 * @template Key 分页键的类型。这通常是页码(Int)、网络光标(String)或数据库查询的排序值。
 * @template Value 加载的数据项的类型。
 */
export abstract class PagingSource<Key, Value> {
  private _invalid: boolean = false;
  
  /**
   * 当 PagingSource 失效时调用，用于通知需要一个新的 PagingSource。
   */
  public onInvalidate: (() => void) | null = null;

  // 置顶相关状态（通用）
  protected topContactsCache: Value[] = [];
  protected isTopExpanded: boolean = true;

  // 筛选相关状态（通用）
  protected filterType: number = 0;
  protected jobId: number = 0;
  
  /**
   * 触发数据源失效。
   * 当底层数据集发生变化，无法再保证增量加载的连续性时调用。
   * 例如，数据库被清空、网络数据被强制刷新等。
   */
  invalidate(): void {
    this._invalid = true;
    this.onInvalidate?.();
  }

  /**
   * 检查数据源是否已失效。
   */
  get invalid(): boolean {
    return this._invalid;
  }
  
  /**
   * 加载一页数据的核心抽象方法。
   * Pager 会调用这个方法来加载数据以响应UI的滚动事件。
   *
   * @param params 加载参数，包含分页键和加载大小。
   * @returns 返回一个包含加载数据的 Promise。
   */
  abstract load(params: LoadParams<Key>): Promise<LoadResult<Key, Value>>;

  /**
   * 获取用于初始（刷新）加载的分页键。
   * 当一个新的 Pager 开始加载数据时，此方法返回的键将用于第一次调用 `load()`。
   * 重构后不再依赖PagingState，简化为直接返回刷新键。
   *
   * @param state 保留参数用于兼容性，可以传null
   * @returns 返回初始加载的键，如果无法确定则返回 null。
   */
  abstract getRefreshKey(state: PagingState<Key, Value> | null): Key | null;

  abstract getAllCount() :  Promise<number>;

  /**
   * 设置置顶展开状态
   */
  public setTopExpanded(expanded: boolean): void {
    this.isTopExpanded = expanded;
  }

  /**
   * 获取置顶展开状态
   */
  public getTopExpanded(): boolean {
    return this.isTopExpanded;
  }

  /**
   * 设置筛选条件
   */
  public setFilter(filterType: number, jobId?: number): void {
    this.filterType = filterType;
    this.jobId = jobId || 0;
  }
} 