/**
 * @file PagingDataAdapter.ets
 * @brief Provides a generic adapter to connect a Pager to ArkUI list components.
 */

import { Logger } from 'lib_zputils/Index';
import { BasicDataSource } from 'lib_zpui/Index';
import { Pager } from './Pager';
import { LoadStates, LoadStateType, PagingState, LoadResult, LoadResultPage } from './PagingSource';
import { DataInvalidationCapable } from './invalidation/DataSourceRegistry';
import { DataItemIdentifier } from './DiffTypes';
import { IncrementalDataUpdater } from './IncrementalDataUpdater';


/**
 * 简化的可见区域信息
 */
interface VisibleRange {
  first: number; // 第一个可见项索引
  last: number; // 最后一个可见项索引
}

/**
 * 页面中的锚点位置信息
 * 简化版本：使用pages数组索引代替pageIndex
 */
interface PageLocation<K, T> {
  arrayIndex: number; // 页面在pages数组中的索引
  page: PageData<K, T>; // 页面引用
}

/**
 * 锚点查找结果
 */
interface AnchorSearchResult<K, T> {
  location: PageLocation<K, T>;
}

/**
 * 页面数据结构 - 每页都包含完整的分页信息
 * 优化版本：移除loadDirection，增加全量数据起始位置
 */
interface PageData<K, T> {
  data: T[]; // 页面数据
  prevKey: K | null; // 上一页的键
  nextKey: K | null; // 下一页的键
  globalStartIndex: number; // 在全量数据中的起始索引位置
}


const TAG = 'PagingDataAdapter';


/**
 * PagingDataAdapter 的非泛型接口，用于注册表存储
 */
export interface IAdapter {
  refresh(): Promise<void>;

  destroy(): void;
}


//{ page: PageData<K, T>, pageIndex: number, indexInPage: number }
export class PageInfo<K, T> {
  page?: PageData<K, T>;

  pageIndex: number = -1;

  indexInPage: number = -1;
}

/**
 * 占位对象 - 用于保持数据源长度稳定
 */
export class PlaceholderItem {
  // 空类，通过instanceof判断类型
}

/**
 * 包装后的数据项 - 可能是真实数据或占位对象
 */
export type WrappedDataItem<T> = T | PlaceholderItem;

/**
 * 判断是否为占位对象
 */
export function isPlaceholder<T>(item: WrappedDataItem<T>): Boolean {
  return item instanceof PlaceholderItem;
}

/**
 * 页面距离信息 - 用于智能清理
 */
interface PageDistanceInfo<K, T> {
  page: PageData<K, T>;
  arrayIndex: number;
  distance: number;
  dataCount: number;
}

/**
 * 加载状态事件监听器
 */
export interface LoadStateListener {
  onLoadStateChanged: (loadStates: LoadStates) => void;
}

/**
 * PagingDataAdapter 是连接 Pager 和 UI 组件的桥梁。
 * 重构后的Adapter成为唯一的数据存储和管理者。
 * 它监听 Pager 的数据动作，并实现智能的内存管理。
 *
 * @template T 列表项的数据类型
 */
export class PagingDataAdapter<K, T> implements IAdapter, DataInvalidationCapable {
  private readonly pager: Pager<K, T>;

  private readonly dataSource: BasicDataSource<WrappedDataItem<T>>;

  private readonly dataItemIdentifier?: DataItemIdentifier<WrappedDataItem<T>>; // 可选的数据项标识符

  // Diff优化相关
  private incrementalUpdater?: IncrementalDataUpdater<WrappedDataItem<T>>;

  private loadStateListeners: LoadStateListener[] = [];

  private lastLoadStates: LoadStates | null = null;

  // 以页为单位的数据管理
  private pages: PageData<K, T>[] = []; // 页面列表

  // 全量数据（包含占位对象） - 展示给UI的完整数据源
  private fullDataList: Array<WrappedDataItem<T>> = [];

  private readonly maxCacheSize: number; // 最大缓存大小，超过时触发清除

  // 预加载控制
  private readonly preloadThreshold: number; // 距离边界多少项时开始预加载

  // 简化的可见区域管理
  private currentVisibleRange: VisibleRange | null = null; // 当前可见区域

  // 防重复刷新控制
  private isReloading: boolean = false;

  constructor(pager: Pager<K, T>, dataItemIdentifier?: DataItemIdentifier<WrappedDataItem<T>>) {
    this.pager = pager;
    this.dataSource = new BasicDataSource<WrappedDataItem<T>>();
    this.dataItemIdentifier = dataItemIdentifier;

    // 从 pager 的配置中获取预加载距离和最大缓存大小
    this.preloadThreshold = pager.config.prefetchDistance;
    this.maxCacheSize = pager.config.maxSize;


    // 初始化增量更新器
    if (dataItemIdentifier) {
      this.incrementalUpdater = new IncrementalDataUpdater(this.dataSource, dataItemIdentifier);
      Logger.debug(TAG, 'placeholder-aware updater initialized');
    } else {
      Logger.debug(TAG, 'no data identifier provided, using full updates');
    }
  }

  async invalidateAndReload(): Promise<void> {
    // 防止重复刷新
    if (this.isReloading) {
      Logger.debug(TAG, 'already reloading, skipping duplicate invalidateAndReload');
      return;
    }

    this.isReloading = true;
    Logger.debug(TAG, 'invalidating and reloading data');

    try {

      // 🔥 使用公共方法查找锚点
      const anchorInfo = this.findCurrentAnchor();
      if (!anchorInfo) {
        Logger.debug(TAG, 'cannot find anchor, skipping invalidateAndReload');
        return;
      }

      // 🔥 清除所有待处理的队列请求
      Logger.debug(TAG, 'clearing all pending queue requests before invalidate');
      this.pager.clearRequestQueues();

      // 🔥 使用公共方法计算刷新key
      const refreshKey = this.calculateRefreshKey(anchorInfo);
      Logger.debug(TAG, `using refresh key: ${JSON.stringify(refreshKey)}`);

      // 执行刷新
      const result = await this.pager.refresh(refreshKey);

      if (result?.type === 'PAGE') {
        Logger.debug(TAG,
          `refresh result: nextKey=${JSON.stringify(result.nextKey)}, prevKey=${JSON.stringify(result.prevKey)}`);

        // 🔥 使用公共方法转换为占位对象
        this.convertPlaceholdersInFullList();

        // 智能复用原有位置
        const existingPage = anchorInfo.location.page;
        const firstPageStartIndex = existingPage ? existingPage.globalStartIndex : 0;

        // 🔥 使用公共方法创建页面
        const updatePage = this.createPageData(result.data, result.prevKey, result.nextKey, firstPageStartIndex);
        this.pages = [updatePage];
        this.fillFullDataListWithPage(updatePage);

        // 🔥 处理refresh页面的边界情况
        this.handlePageBoundary(updatePage, firstPageStartIndex, 'append');
        this.handlePageBoundary(updatePage, firstPageStartIndex, 'prepend');

        // 🔥 使用公共方法执行append和prepend
        if (result.nextKey) {
          await this.performInvalidateAppend(result.nextKey, firstPageStartIndex + result.data.length);
        }

        if (result.prevKey) {
          await this.performInvalidatePrepend(result.prevKey, firstPageStartIndex);
        }

        // 更新UI
        this.updateDataSource(this.fullDataList, 'invalidate_reload');
        Logger.debug(TAG, 'invalidate and reload completed');
      } else {
        Logger.debug(TAG, 'refresh result is not a page, skipping invalidateAndReload')
      }
    } finally {
      this.isReloading = false;
    }
  }

  /**
   * 初始化适配器并开始监听数据
   */
  public async initialize(): Promise<void> {
    Logger.debug(TAG, 'initializing adapter');

    // 🔥 第一步：获取总数据量并初始化占位对象
    await this.initializeWithPlaceholders();

    // 🔥 第二步：加载真实数据
    await this.refresh();
  }

  /**
   * 初始化占位对象 - 先获取总数量，创建全量占位对象
   */
  private async initializeWithPlaceholders(): Promise<void> {
    try {
      // 🔥 从数据源获取总数量
      const totalCount = await this.pager.getAllCount();
      Logger.debug(TAG, `total data count from source: ${totalCount}`);

      if (totalCount > 0) {
        // 🔥 创建全量占位对象列表
        for (let index = 0; index < totalCount; index++) {
          const element = new PlaceholderItem();
          this.fullDataList.push(element)
        }

        // 🔥 立即更新UI显示占位对象
        this.updateDataSource(this.fullDataList, 'initialize_placeholders');

        Logger.debug(TAG, `initialized with ${totalCount} placeholder items`);
      } else {
        // 没有数据，初始化为空列表
        this.fullDataList = [];
        this.updateDataSource(this.fullDataList, 'initialize_empty');
        Logger.debug(TAG, 'initialized with empty data list');
      }
    } catch (error) {
      Logger.error(TAG, 'failed to get total count, fallback to normal initialization', error);
      // 获取总数失败，回退到正常初始化
      this.fullDataList = [];
    }
  }

  /**
   * 处理刷新结果 - 支持已存在页面的替换
   */
  private handleRefreshResult(result: LoadResultPage<K, T>): void {
    Logger.debug(TAG, 'handling refresh result with ' + result.data.length + ' items');

    // 创建数据数组，避免使用扩展运算符
    const dataArray: T[] = [];
    for (let i = 0; i < result.data.length; i++) {
      dataArray.push(result.data[i]);
    }

    // 🔥 检查是否已存在相同的页面
    const existingPage = this.findExistingPage(result.prevKey, result.nextKey);
    const firstPageStartIndex = existingPage ? existingPage.globalStartIndex : 0;
    if (existingPage) {
      // 🔥 页面已存在：使用原有的globalStartIndex，替换数据
      Logger.debug(TAG, `refresh: updating existing page at globalStartIndex ${existingPage.globalStartIndex}`);


      // 先将原页面在fullDataList中转为占位对象
      this.convertPageToPlaceholdersInFullList(existingPage);

      // 更新页面数据
      existingPage.data = dataArray;
      existingPage.prevKey = result.prevKey;
      existingPage.nextKey = result.nextKey;

      // 重新填充到fullDataList
      this.fillFullDataListWithPage(existingPage);

    } else {
      const newPageStartIndex = 0;
      const newPage: PageData<K, T> = {
        data: dataArray,
        prevKey: result.prevKey,
        nextKey: result.nextKey,
        globalStartIndex: newPageStartIndex
      };

      Logger.debug(TAG, `refresh: creating new page at globalStartIndex ${newPageStartIndex}`);

      // 重置所有状态，只保留新页面
      this.pages = [newPage];

      // 填充到fullDataList
      this.fillFullDataListWithPage(newPage);
    }

    // 🔥 更新数据源使用fullDataList
    this.updateDataSource(this.fullDataList, 'refresh');

    // 更新加载状态
    this.lastLoadStates = this.pager.getCurrentLoadStates();
    this.notifyLoadStateChanged();

    Logger.debug(TAG,
      'refresh completed with ' + result.data.length + ' items, prevKey=' + result.prevKey + ', nextKey=' +
      result.nextKey);
  }

  /**
   * 将页面数据填充到fullDataList中
   */
  private fillFullDataListWithPage(page: PageData<K, T>): void {
    for (let i = 0; i < page.data.length; i++) {
      const globalIndex = page.globalStartIndex + i;
      if (globalIndex >= 0 && globalIndex < this.fullDataList.length) {
        this.fullDataList[globalIndex] = page.data[i] as WrappedDataItem<T>;
      }

      if (globalIndex === this.fullDataList.length) {
        this.fullDataList.push(page.data[i] as WrappedDataItem<T>);
        Logger.debug(TAG, `fullDataList extended to ${this.fullDataList.length} items`);
      }

    }
    Logger.debug(TAG,
      `filled fullDataList with page data at position [${page.globalStartIndex}, ${page.globalStartIndex +
      page.data.length - 1}]`);
  }

  /**
   * 查找已存在的页面（基于prevKey和nextKey）
   */
  private findExistingPage(prevKey: K | null, nextKey: K | null): PageData<K, T> | null {
    for (const page of this.pages) {
      if (page.prevKey === prevKey && page.nextKey === nextKey) {
        return page;
      }
    }
    return null;
  }

  /**
   * 将页面数据在fullDataList中转为占位对象
   */
  private convertPageToPlaceholdersInFullList(page: PageData<K, T>): void {
    for (let i = 0; i < page.data.length; i++) {
      const globalIndex = page.globalStartIndex + i;
      if (globalIndex >= 0 && globalIndex < this.fullDataList.length) {
        this.fullDataList[globalIndex] = new PlaceholderItem();
      }
    }
    Logger.debug(TAG,
      `converted page to placeholders in fullDataList at position [${page.globalStartIndex}, ${page.globalStartIndex +
      page.data.length - 1}]`);
  }

  /**
   * 将fullDataList中的对象转为占位对象
   */
  private convertPlaceholdersInFullList(): void {
    for (let i = 0; i < this.fullDataList.length; i++) {
      if (!isPlaceholder(this.fullDataList[i])) {
        this.fullDataList[i] = new PlaceholderItem();
      }
    }
  }

  /**
   * 处理追加结果 - 支持已存在页面的替换
   */
  private handleAppendResult(result: LoadResultPage<K, T>): void {
    Logger.debug(TAG, 'handling append result with ' + result.data.length + ' items');

    if (result.data.length === 0) {
      Logger.debug(TAG, `no more data to append`);
      return;
    }

    // 创建数据数组，避免使用扩展运算符
    const dataArray: T[] = [];
    for (let i = 0; i < result.data.length; i++) {
      dataArray.push(result.data[i]);
    }

    // 🔥 新页面：计算新的globalStartIndex
    let newStartIndex = 0;
    if (this.pages.length > 0) {
      const lastPage = this.pages[this.pages.length - 1];
      newStartIndex = lastPage.globalStartIndex + lastPage.data.length;
    }

    // 🔥 使用公共方法确保fullDataList有足够空间
    newStartIndex = this.ensureFullDataListCapacity(newStartIndex, dataArray.length);

    const newPage: PageData<K, T> = {
      data: dataArray,
      prevKey: result.prevKey,
      nextKey: result.nextKey,
      globalStartIndex: newStartIndex
    };

    Logger.debug(TAG, `append: creating new page at globalStartIndex ${newStartIndex}`);

    // 添加到页面列表末尾
    this.pages.push(newPage);

    // 填充到fullDataList
    this.fillFullDataListWithPage(newPage);

    // 🔥 使用公共方法处理边界情况
    this.handlePageBoundary(newPage, newStartIndex, 'append');


    // 🔥 更新数据源使用fullDataList
    this.updateDataSource(this.fullDataList, 'append');

    // 更新加载状态
    this.lastLoadStates = this.pager.getCurrentLoadStates();
    this.notifyLoadStateChanged();

    // 在数据变化后进行延时清除
    this.smartCleanupPages();

    Logger.debug(TAG,
      'append completed: added page at index ' + (this.pages.length - 1) + ' with ' + result.data.length +
        ' items, prevKey=' + result.prevKey + ', nextKey=' + result.nextKey);
  }

  /**
   * 处理前置结果 - 支持已存在页面的替换
   */
  private handlePrependResult(result: LoadResultPage<K, T>): void {
    Logger.debug(TAG, 'handling prepend result with ' + result.data.length + ' items');

    if (result.data.length === 0) {
      Logger.debug(TAG, `no more data to prepend`);
      return;
    }

    // 创建数据数组，避免使用扩展运算符
    const dataArray: T[] = [];
    for (let i = 0; i < result.data.length; i++) {
      dataArray.push(result.data[i]);
    }

    // 🔥 新页面：计算新的globalStartIndex
    let newStartIndex = 0;
    if (this.pages.length > 0) {
      const firstPage = this.pages[0];
      newStartIndex = firstPage.globalStartIndex - result.data.length;
    }

    // 🔥 使用公共方法确保fullDataList有足够空间
    newStartIndex = this.ensureFullDataListCapacity(newStartIndex, dataArray.length);

    const newPage: PageData<K, T> = {
      data: dataArray,
      prevKey: result.prevKey,
      nextKey: result.nextKey,
      globalStartIndex: newStartIndex
    };

    Logger.debug(TAG, `prepend: creating new page at globalStartIndex ${newStartIndex}`);

    // 添加到页面列表开头
    this.pages.unshift(newPage);

    // 填充到fullDataList
    this.fillFullDataListWithPage(newPage);

    // 🔥 使用公共方法处理边界情况
    this.handlePageBoundary(newPage, newStartIndex, 'prepend');

    // 🔥 更新数据源使用fullDataList
    this.updateDataSource(this.fullDataList, 'prepend');

    // 更新加载状态
    this.lastLoadStates = this.pager.getCurrentLoadStates();
    this.notifyLoadStateChanged();

    this.smartCleanupPages();

    Logger.debug(TAG, 'prepend completed: added page at index 0 with ' + result.data.length + ' items, prevKey=' +
    result.prevKey?.toString() + ', nextKey=' + result.nextKey?.toString());
  }

  /**
   * 简化的数据源更新方法
   */
  private updateDataSource(newData: WrappedDataItem<T>[],
    updateType: 'refresh' | 'append' | 'prepend' | 'cleanup' | 'initialize_placeholders' | 'initialize_empty' | 'invalidate_reload'): void {
    if (this.incrementalUpdater) {
      // 使用占位对象感知的更新器
      this.incrementalUpdater.updateData(newData);
    } else {
      // 降级为全量更新
      this.dataSource.setNewData(newData);
      Logger.debug(TAG, 'fallback to full update: ' + newData.length + ' items, type=' + updateType);
    }
  }




  public getPageAllData(): T[] {
    const data: T[] = [];
    for (let index = 0; index < this.pages.length; index++) {
      data.push(...this.pages[index].data)

    }
    return data;
  }

  /**
   * 简化的智能清除页面 - 保留当前锚点页面的上下各两个页面
   */
  private smartCleanupPages(): void {
    const totalDataCount = this.getTotalDataCountFromPages();

    if (totalDataCount <= this.maxCacheSize) {
      Logger.debug(TAG, `cleanup skipped: data count ${totalDataCount} <= threshold ${this.maxCacheSize}`);
      return;
    }

    // 基于当前可见区域找到中心页面
    if (!this.currentVisibleRange) {
      Logger.debug(TAG, 'no current visible range, skipping page cleanup');
      return;
    }

    const centerIndex = this.getCurrentCenterIndex();
    const anchorLocation = this.findAnchorByGlobalIndex(centerIndex);
    if (!anchorLocation) {
      Logger.debug(TAG, 'center index not found in pages, skipping cleanup');
      return;
    }

    const originalPageCount = this.pages.length;
    const anchorArrayIndex = anchorLocation.location.arrayIndex;

    // 计算每个页面到锚点页面的距离（简单的页面索引距离）
    const pageDistances: Array<PageDistanceInfo<K, T>> = [];

    for (let i = 0; i < this.pages.length; i++) {
      const page = this.pages[i];
      const distance = Math.abs(i - anchorArrayIndex);  // 页面索引距离

      const pageInfo: PageDistanceInfo<K, T> = {
        page: page,
        arrayIndex: i,
        distance: distance,
        dataCount: page.data.length
      };
      pageDistances.push(pageInfo);
    }

    // 按距离排序：距离远的排在前面（优先删除）
    pageDistances.sort((a, b) => b.distance - a.distance);
    const pagesToKeep: PageData<K, T>[] = [];

    let tempSize = totalDataCount;
    for (const pageInfo of pageDistances) {
      if (tempSize <= this.maxCacheSize) {
        pagesToKeep.push(pageInfo.page);
      }
      tempSize -= pageInfo.dataCount;
    }

    // 按原来的数组索引重新排序保留的页面
    pagesToKeep.sort((a, b) => {
      const indexA = this.pages.indexOf(a);
      const indexB = this.pages.indexOf(b);
      return indexA - indexB;
    });

    if (pagesToKeep.length < this.pages.length) {
      this.pages = pagesToKeep;
      const cleanedPageCount = originalPageCount - this.pages.length;
      const finalDataCount = this.getTotalDataCountFromPages();

      Logger.debug(TAG, 'smart page cleanup by distance: removed ' + cleanedPageCount + ' pages, ' + originalPageCount + ' -> ' + this.pages.length);
      Logger.debug(TAG, 'data count: ' + totalDataCount + ' -> ' + finalDataCount + ' (target: <=' + this.maxCacheSize + ')');
      Logger.debug(TAG, 'anchor at page ' + anchorArrayIndex);
    }
    this.pages = pagesToKeep;
    this.convertPlaceholdersInFullList();
    for (let index = 0; index < this.pages.length; index++) {
      this.fillFullDataListWithPage(this.pages[index]);
    }
    this.updateDataSource(this.fullDataList, 'cleanup');
  }

  /**
   * 获取当前可见区域的中心索引
   */
  private getCurrentCenterIndex(): number {
    if (!this.currentVisibleRange) {
      return 0;
    }
    return Math.floor((this.currentVisibleRange.first + this.currentVisibleRange.last) / 2);
  }


  /**
   * 通过全局索引找到对应的数据项和页面位置
   * 基于pages数组的连续性进行查找
   */
  private findAnchorByGlobalIndex(globalIndex: number): AnchorSearchResult<K, T> | null {
    for (let arrayIdx = 0; arrayIdx < this.pages.length; arrayIdx++) {
      const page = this.pages[arrayIdx];
      if (globalIndex >= page.globalStartIndex && globalIndex <= page.globalStartIndex + page.data.length) {
        const location: PageLocation<K, T> = {
          arrayIndex: arrayIdx,
          page: page
        };

        const result: AnchorSearchResult<K, T> = { location };
        Logger.debug(TAG, 'found anchor at index ' + globalIndex + ' in page ' + location.arrayIndex);
        return result;
      }
    }

    return null;
  }

  /**
   * 基于占位对象检查是否需要预加载
   * 直接检查预加载位置是否为占位对象，简单高效
   */
  private checkPreloadByPlaceholder(first: number, last: number): void {
    if (this.pages.length === 0) {
      return;
    }

    const totalCount = this.fullDataList.length;

    // 向下预加载：检查 last + preloadThreshold 位置是否为占位对象
    const appendCheckIndex = last + this.preloadThreshold;
    if (appendCheckIndex < totalCount) {
      const appendCheckItem = this.fullDataList[appendCheckIndex];
      if (isPlaceholder(appendCheckItem)) {
        const lastNextKey = this.pages[this.pages.length - 1].nextKey;
        if (lastNextKey !== null) {
          Logger.debug(TAG, 'triggering append preload: found placeholder at index ' + appendCheckIndex);
          this.pager.loadAppendWithPrefetchPriority(lastNextKey).then(result => {
            if (result?.type === 'PAGE') {
              this.handleAppendResult(result);
            }
          });
        }
      }
    } else {
      if (this.pages[this.pages.length - 1].nextKey) {
        Logger.debug(TAG, 'triggering prepend preload: found placeholder at index ' + totalCount);
        this.pager.loadAppendWithPrefetchPriority(this.pages[0].nextKey).then(result => {
          if (result?.type === 'PAGE') {
            this.handleAppendResult(result);
          }
        });
      }
    }

    // 向上预加载：检查 first - preloadThreshold 位置是否为占位对象
    const prependCheckIndex = first - this.preloadThreshold;
    if (prependCheckIndex >= 0) {
      const prependCheckItem = this.fullDataList[prependCheckIndex];
      if (isPlaceholder(prependCheckItem)) {
        const firstPrevKey = this.pages[0].prevKey;
        if (firstPrevKey !== null) {
          Logger.debug(TAG, 'triggering prepend preload: found placeholder at index ' + prependCheckIndex);
          this.pager.loadPrependWithPrefetchPriority(firstPrevKey).then(result => {
            if (result?.type === 'PAGE') {
              this.handlePrependResult(result);
            }
          });
        }
      }
    } else {
      if (this.pages[0].prevKey) {
        Logger.debug(TAG, 'triggering prepend preload: found placeholder at index ' + -1);
        this.pager.loadPrependWithPrefetchPriority(this.pages[0].prevKey).then(result => {
          if (result?.type === 'PAGE') {
            this.handlePrependResult(result);
          }
        });
      }
    }

  }

  /**
   * 从页面结构计算总数据量（避免合并数据）
   */
  private getTotalDataCountFromPages(): number {
    let totalCount = 0;
    for (let i = 0; i < this.pages.length; i++) {
      totalCount += this.pages[i].data.length;
    }
    return totalCount;
  }

  // --- Public API for UI ---

  /**
   * 获取提供给 LazyForEach 的数据源
   */
  public getDataSource(): BasicDataSource<WrappedDataItem<T>> {
    return this.dataSource;
  }

  /**
   * 手动触发一次数据刷新
   */
  public async refresh(): Promise<void> {
    Logger.debug(TAG, 'manual refresh triggered');

    // 🔥 清除所有待处理的队列请求
    Logger.debug(TAG, 'clearing all pending queue requests before refresh');
    this.pager.clearRequestQueues();

    let result: LoadResult<K, T> | null = null;
    result = await this.pager.init();
    if (result?.type == 'PAGE') {
      this.handleRefreshResult(result);
    }
  }

  /**
   * 重试最后一次失败的操作
   */
  public async retry(): Promise<void> {
    Logger.debug(TAG, 'retry triggered');
    const states = this.lastLoadStates;
    if (!states) {
      return;
    }

    if (states.refresh.type === LoadStateType.Error) {
      await this.refresh();
    } else if (states.append.type === LoadStateType.Error) {
      const result = await this.pager.loadAppend();
      if (result?.type == 'PAGE') {
        this.handleAppendResult(result);
      }
    } else if (states.prepend.type === LoadStateType.Error) {
      const result = await this.pager.loadPrepend();
      if (result?.type == 'PAGE') {
        this.handlePrependResult(result);
      }
    }
  }

  /**
   * UI组件的滚动事件回调（简化版本）
   * @param first 第一个可见项的索引
   * @param last 最后一个可见项的索引
   */
  public onScrollIndex(first: number, last: number): void {
    // 更新当前可见区域
    this.currentVisibleRange = { first, last };

    // 直接检查预加载位置是否为占位对象
    this.checkPreloadByPlaceholder(first, last);
  }

  /**
   * 获取当前列表中的项目总数
   * 优化：直接计算页面数据量，避免合并数据
   */
  public getItemCount(): number {
    return this.getTotalDataCountFromPages();
  }


  // --- Listeners Management ---


  public addLoadStateListener(listener: LoadStateListener): void {
    this.loadStateListeners.push(listener);
  }

  public removeLoadStateListener(listener: LoadStateListener): void {
    const index = this.loadStateListeners.indexOf(listener);
    if (index > -1) {
      this.loadStateListeners.splice(index, 1);
    }
  }

  private notifyLoadStateChanged(): void {
    if (this.lastLoadStates) {
      this.loadStateListeners.forEach(l => l.onLoadStateChanged(this.lastLoadStates!));
    }
  }


  public destroy(): void {
    Logger.debug(TAG, 'destroying adapter');
    this.pager.invalidate();
    this.loadStateListeners = [];
    this.pages = [];
    this.currentVisibleRange = null;
  }

  /**
   * 获取适配器标识
   */
  public getAdapterId(): string {
    return `PagingDataAdapter_${Date.now()}`;
  }

  /**
   * 获取当前的 PagingState
   * 基于可见区域保存滚动位置，包含锚点ID用于优化刷新
   */
  public getCurrentPagingState(): PagingState<K, T> | null {
    if (this.pages.length === 0) {
      return null;
    }

    // 使用当前可见区域的中心位置
    const anchorPosition = this.getCurrentCenterIndex();

    // 获取锚点项的ID（如果有数据标识符）
    let anchorItemId: string | undefined = undefined;
    if (this.dataItemIdentifier && anchorPosition >= 0 && anchorPosition < this.fullDataList.length) {
      const anchorItem = this.fullDataList[anchorPosition];
      if (anchorItem && !isPlaceholder(anchorItem)) {
        anchorItemId = this.dataItemIdentifier.getId(anchorItem);
      }
    }
    Logger.debug(TAG, `getCurrentPagingState: anchorPosition=${anchorPosition}, anchorItemId=${anchorItemId}`);

    return {
      pages: this.pages,
      anchorItemId: anchorItemId, // 锚点项ID，用于优化刷新定位
      config: this.pager.config
    };
  }

  /**
   * 扩展fullDataList到指定长度（向后扩展）
   * 用于APPEND操作时确保有足够的占位空间
   */
  private extendFullDataListToIndex(targetIndex: number): void {
    if (targetIndex >= this.fullDataList.length) {
      Logger.debug(TAG, `extending fullDataList from ${this.fullDataList.length} to ${targetIndex} items`);
      for (let i = this.fullDataList.length; i <= targetIndex; i++) {
        this.fullDataList.push(new PlaceholderItem());
      }
    }
  }

  /**
   * 向前扩展fullDataList（向前插入占位对象）
   * 用于PREPEND操作时处理负索引情况
   * @param negativeStartIndex 负的起始索引
   * @returns 调整后的起始索引（0）
   */
  private prependPlaceholdersToFullDataList(negativeStartIndex: number): number {
    const insertCount = -negativeStartIndex;
    Logger.debug(TAG, `prepending ${insertCount} placeholders to fullDataList`);

    // 向前插入占位对象
    for (let i = 0; i < insertCount; i++) {
      this.fullDataList.unshift(new PlaceholderItem());
    }

    // 调整所有现有页面的globalStartIndex
    for (let index = 0; index < this.pages.length; index++) {
      this.pages[index].globalStartIndex += insertCount;
    }

    return 0; // 返回调整后的起始索引
  }

  /**
   * 确保fullDataList有足够的空间容纳新页面
   * 处理APPEND和PREPEND操作的空间分配
   * @param startIndex 页面的起始索引
   * @param dataLength 页面数据长度
   * @returns 调整后的起始索引
   */
  private ensureFullDataListCapacity(startIndex: number, dataLength: number): number {
    if (startIndex < 0) {
      // PREPEND情况：向前扩展
      return this.prependPlaceholdersToFullDataList(startIndex);
    } else {
      // APPEND情况：向后扩展
      this.extendFullDataListToIndex(startIndex + dataLength - 1);
      return startIndex;
    }
  }

  /**
   * 处理APPEND操作的边界情况
   * 当到达数据末尾时（nextKey为null），裁剪fullDataList移除多余的占位对象
   * @param page 新添加的页面
   * @param startIndex 页面的起始索引
   */
  private handleAppendBoundary(page: PageData<K, T>, startIndex: number): void {
    if (!page.nextKey) {
      const endIndex = startIndex + page.data.length;
      Logger.debug(TAG,
        `append boundary reached: trimming fullDataList from ${this.fullDataList.length} to ${endIndex} items`);
      this.fullDataList = this.fullDataList.slice(0, endIndex);
    }
  }

  /**
   * 处理PREPEND操作的边界情况
   * 当到达数据开头时（prevKey为null），裁剪fullDataList移除前面多余的占位对象
   * @param page 新添加的页面
   * @param startIndex 页面的起始索引
   */
  private handlePrependBoundary(page: PageData<K, T>, startIndex: number): void {
    if (!page.prevKey) {
      Logger.debug(TAG, `prepend boundary reached: trimming fullDataList from start, removing ${startIndex} items`);
      this.fullDataList = this.fullDataList.slice(startIndex);

      // 调整所有页面的globalStartIndex
      for (let index = 0; index < this.pages.length; index++) {
        this.pages[index].globalStartIndex -= startIndex;
      }
    }
  }

  /**
   * 处理页面边界的统一入口
   * 根据操作类型自动选择合适的边界处理方法
   * @param page 页面数据
   * @param startIndex 页面起始索引
   * @param operation 操作类型
   */
  private handlePageBoundary(page: PageData<K, T>, startIndex: number, operation: 'append' | 'prepend'): void {
    if (operation === 'append') {
      this.handleAppendBoundary(page, startIndex);
    } else if (operation === 'prepend') {
      this.handlePrependBoundary(page, startIndex);
    }
  }

  /**
   * 执行invalidateAndReload中的append操作
   * @param nextKey 用于append的key
   * @param startIndex append的起始索引
   */
  private async performInvalidateAppend(nextKey: K | null, startIndex: number): Promise<void> {
    const appendResult = await this.pager.loadAppend(nextKey);
    if (appendResult?.type === 'PAGE') {
      const appendPage = this.createPageData(
        appendResult.data,
        appendResult.prevKey,
        appendResult.nextKey,
        startIndex
      );
      this.pages.push(appendPage);
      this.fillFullDataListWithPage(appendPage);

      // 🔥 处理边界情况
      this.handlePageBoundary(appendPage, startIndex, 'append');

      Logger.debug(TAG, `invalidate append completed: ${appendResult.data.length} items at index ${startIndex}`);
    }
  }

  /**
   * 执行invalidateAndReload中的prepend操作
   * @param prevKey 用于prepend的key
   * @param currentStartIndex 当前页面的起始索引
   */
  private async performInvalidatePrepend(prevKey: K | null, currentStartIndex: number): Promise<void> {
    const prependResult = await this.pager.loadPrepend(prevKey);
    if (prependResult?.type === 'PAGE') {
      const prependStartIndex = Math.max(0, currentStartIndex - prependResult.data.length);
      const prependPage = this.createPageData(
        prependResult.data,
        prependResult.prevKey,
        prependResult.nextKey,
        prependStartIndex
      );
      this.pages.unshift(prependPage);
      this.fillFullDataListWithPage(prependPage);

      // 🔥 处理边界情况
      this.handlePageBoundary(prependPage, prependStartIndex, 'prepend');

      Logger.debug(TAG,
        `invalidate prepend completed: ${prependResult.data.length} items at index ${prependStartIndex}`);
    }
  }

  /**
   * 查找当前中心位置的锚点信息
   * 用于invalidateAndReload时确定刷新的起始位置
   * @returns 锚点位置信息，如果找不到则返回null
   */
  private findCurrentAnchor(): AnchorSearchResult<K, T> | null {
    const centerIndex = this.getCurrentCenterIndex();
    const anchorLocation = this.findAnchorByGlobalIndex(centerIndex);

    if (!anchorLocation) {
      Logger.debug(TAG, 'center index not found in pages, cannot find anchor');
      return null;
    }

    Logger.debug(TAG, `found anchor at centerIndex: ${centerIndex}, page: ${anchorLocation.location.arrayIndex}`);
    return anchorLocation;
  }

  /**
   * 计算刷新时使用的key
   * 根据锚点位置决定使用哪个key进行刷新
   * @param anchorLocation 锚点位置信息
   * @returns 刷新用的key
   */
  private calculateRefreshKey(anchorLocation: AnchorSearchResult<K, T>): K | null {
    // 如果锚点在第一个页面的第一个位置，使用null（从头开始）
    // 否则使用前一个页面的nextKey
    const refreshKey = anchorLocation.location.arrayIndex === 0 ?
      null :
    this.pages[anchorLocation.location.arrayIndex - 1].nextKey;

    Logger.debug(TAG, `calculated refresh key: ${JSON.stringify(refreshKey)}`);
    return refreshKey;
  }

  /**
   * 创建页面对象的辅助方法
   * @param data 页面数据
   * @param prevKey 前一页key
   * @param nextKey 下一页key
   * @param globalStartIndex 全局起始索引
   * @returns 页面对象
   */
  private createPageData(data: T[], prevKey: K | null, nextKey: K | null, globalStartIndex: number): PageData<K, T> {
    return {
      data: data,
      prevKey: prevKey,
      nextKey: nextKey,
      globalStartIndex: globalStartIndex
    };
  }
}