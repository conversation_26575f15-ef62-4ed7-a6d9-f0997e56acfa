import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactDao } from '../db/dao/ContactDao';
import { AllContactDao } from '../db/dao/AllContactDao';
import { Kernel } from '../../Kernel';
import { Logger } from 'lib_zputils/Index';
import { PagingSource, LoadParams, LoadResult, LoadResultPage, PagingState, LoadDirection } from './PagingSource';
import { ContactDataItemIdentifier } from './ContactDataItemIdentifier';
import { PagingKey } from './OnlyContactPagingSource';
import { F2FriendType } from '../f2/F2ContactManager';
import { ContactUtils } from '../ContactUtils';
import { VirtualContactManager } from './VirtualContactManager';


const TAG = 'AllContactPagingSource';

/**
 * 全部联系人分组的分页数据源实现。
 * 继承自通用的 PagingSource，负责从数据库加载所有联系人数据。
 *
 * 与OnlyContactPagingSource的主要差异：
 * 1. 包含置顶联系人的完整处理逻辑（支持抽屉功能）
 * 2. 查询条件更宽泛，包含所有有效联系人（不排除有面试和有交换的联系人）
 * 3. 不支持筛选功能（全部Tab显示所有数据，不应用筛选条件）
 * 4. 🔥 包含虚拟联系人（功能抽屉），保持与F2ContactManager功能同步
 */
export class AllContactPagingSource extends PagingSource<PagingKey, ContactEntity> {
  private contactDao: ContactDao | null = null;

  private allContactDao: AllContactDao | null = null;

  // WeekAge分割线相关状态
  private weekAgeDividerInserted: boolean = false;

  // 🐌 网络延迟模拟配置（与OnlyContactPagingSource保持一致）
  private static ENABLE_SLOW_SIMULATION = false;

  private static SLOW_DELAY_MS = 2000;

  private static RANDOM_DELAY = true;

  constructor() {
    super();

  }


  /**
   * 🐌 模拟网络延迟
   */
  private async simulateNetworkDelay(): Promise<void> {
    if (!AllContactPagingSource.ENABLE_SLOW_SIMULATION) {
      return;
    }

    let delay = AllContactPagingSource.SLOW_DELAY_MS;
    if (AllContactPagingSource.RANDOM_DELAY) {
      const randomFactor = 0.5 + Math.random();
      delay = Math.floor(delay * randomFactor);
    }

    Logger.debug(TAG, `🐌 模拟网络延迟: ${delay}ms`);
    return new Promise<void>((resolve) => {
      setTimeout(() => resolve(), delay);
    });
  }

  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao || !this.allContactDao) {
      try {
        const dbService = Kernel.getInstance().dbService();
        if (!dbService) {
          throw new Error('DatabaseService is not available');
        }

        this.contactDao = await dbService.getContactDao();
        if (!this.contactDao) {
          throw new Error('ContactDao initialization failed - returned null');
        }

        this.allContactDao = new AllContactDao(this.contactDao);

        Logger.debug(TAG, 'ContactDao and AllContactDao initialized successfully');
      } catch (error) {
        Logger.error(TAG, `Failed to initialize DAOs: ${error}`);
        throw new Error(`DAO initialization failed: ${error}`);
      }
    }
  }

  /**
   * 核心加载方法。根据提供的分页键从数据库加载全部联系人数据。
   */
  async load(params: LoadParams<PagingKey>): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      await this.ensureDaoInitialized();
      Logger.debug(TAG, `🚀 开始加载全部联系人数据: ${JSON.stringify(params)}`);

      // 🐌 模拟网络延迟
      await this.simulateNetworkDelay();

      const key = params.key;
      const loadSize = params.loadSize;
      const direction = params.direction;

      let sql: string;
      let sqlParams: (string | number | boolean | null)[];

      // 使用LIMIT + 1技巧来准确判断是否还有更多数据
      const querySize = loadSize + 1;

      // 根据加载方向选择不同的处理方式
      switch (direction) {
        case LoadDirection.INIT:
        case LoadDirection.REFRESH:
          if (!key) {
            // 初始加载或无key刷新：重置状态并加载混合数据（置顶 + 分割线 + 普通联系人）
            this.weekAgeDividerInserted = false;
            return await this.loadMixedData(loadSize);
          } else {
            // 有key的刷新：从指定位置开始，不包含置顶联系人
            sql = this.allContactDao!.buildAllContactAppendSql();
            sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          }
          break;
        case LoadDirection.APPEND:
          if (!key) {
            throw new Error('Key is required for APPEND load');
          }
          sql = this.allContactDao!.buildAllContactAppendSql();
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.PREPEND:
          if (!key) {
            throw new Error('Key is required for PREPEND load');
          }
          sql = this.allContactDao!.buildAllContactPrependSql();
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        default:
          throw new Error(`Unsupported load direction: ${direction}`);
      }

      Logger.debug(TAG, `Executing SQL: ${sql} with params: ${JSON.stringify(sqlParams)}`);

      // 执行查询
      let contacts = await this.contactDao?.rawQueryContacts(sql, sqlParams);
      if (!contacts) {
        Logger.warn(TAG, 'rawQueryContacts returned null, using empty array');
        contacts = [];
      }

      // 验证查询结果
      if (!Array.isArray(contacts)) {
        Logger.error(TAG, `Invalid query result type: ${typeof contacts}`);
        contacts = [];
      }

      // 检查是否还有更多数据
      const hasMoreData = contacts.length > loadSize;
      if (hasMoreData) {
        contacts = contacts.slice(0, loadSize);
      }

      // PREPEND查询的特殊处理：需要reverse以保持UI的降序显示
      if (direction === LoadDirection.PREPEND) {
        contacts.reverse();
      }

      // 🔥 新增：在APPEND加载时检查是否需要插入WeekAge分割线
      if (direction === LoadDirection.APPEND && !this.weekAgeDividerInserted) {
        contacts = await this.insertWeekAgeDividerIfNeeded(contacts);
      }

      // 🔥 新增：根据页面位置混入虚拟联系人
      if (direction === LoadDirection.INIT || direction === LoadDirection.REFRESH ||
        direction === LoadDirection.APPEND || direction === LoadDirection.PREPEND) {
        contacts = await this.mixVirtualContactsForPagePosition(contacts, direction, hasMoreData, querySize);
      }

      Logger.debug(TAG, `final result: ${contacts.length} contacts, hasMore=${hasMoreData}`);

      const prevKey = this.getPrevKey(contacts, direction, hasMoreData);
      const nextKey = this.getNextKey(contacts, direction, hasMoreData);

      const page: LoadResultPage<PagingKey, ContactEntity> = {
        type: 'PAGE',
        data: contacts,
        prevKey: prevKey,
        nextKey: nextKey,
      };
      return page;
    } catch (e) {
      Logger.error(TAG, `load failed: ${e}`);
      return {
        type: 'ERROR',
        error: e instanceof Error ? e : new Error('Unknown error during data loading')
      };
    }
  }

  /**
   * 获取用于刷新的键
   */
  getRefreshKey(state: PagingState<PagingKey, ContactEntity> | null): PagingKey | null {
    // 简化实现：返回null表示从头开始刷新
    return null;
  }

  /**
   * 获取全部联系人总数
   * 包括真实联系人和虚拟联系人
   */
  async getAllCount(): Promise<number> {
    try {
      await this.ensureDaoInitialized();

      // 获取真实联系人数量
      const realCount = await this.allContactDao!.queryAllContactCountSql();

      // 获取虚拟联系人数量
      const virtualContacts = await this.getVirtualContacts();
      const virtualCount = virtualContacts.length;

      const totalCount = realCount + virtualCount;
      Logger.debug(TAG, `getAllCount: ${totalCount} (real: ${realCount}, virtual: ${virtualCount})`);

      return totalCount;
    } catch (e) {
      Logger.error(TAG, `getAllCount failed: ${e}`);
      return 0;
    }
  }

  /**
   * 确定上一页的键。
   * 根据加载方向和数据边界状态正确生成prevKey：
   * - REFRESH/APPEND: 取第一项（时间最新的）
   * - PREPEND: 取最后一项（reverse后的最后一项，即原始时间最新的）
   */
  private getPrevKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    // 对于PREPEND方向，如果没有更多数据，说明已经到达最新数据的边界
    if (direction === LoadDirection.PREPEND && !hasMoreData) {
      Logger.debug(TAG, 'PREPEND reached beginning, no more newer data');
      return null;
    }

    if (direction === LoadDirection.INIT) {
      return null;
    }

    // 对于其他方向，正常生成prevKey
    let keyItem: ContactEntity;
    keyItem = items[0];

    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  /**
   * 确定下一页的键。
   * 根据加载方向和数据边界状态正确生成nextKey：
   * - REFRESH/APPEND: 取最后一项（时间最旧的）
   * - PREPEND: 取第一项（reverse后的第一项，即原始时间最旧的）
   */
  private getNextKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    // 使用LIMIT + 1技巧的结果来准确判断是否还有更多数据
    // 对于APPEND方向，如果没有更多数据，说明已经到达最旧数据的边界
    if ((direction === LoadDirection.APPEND || direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) && !hasMoreData) {
      Logger.debug(TAG, 'APPEND reached end, no more older data');
      return null;
    }

    // 对于其他情况，正常生成nextKey
    let keyItem: ContactEntity;
    keyItem = items[items.length - 1]; // 正常情况下的最后一项
    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  /**
   * 计算排序值（使用聊天时间）
   */
  private calculateSortValue(contact: ContactEntity): number {
    const chatTime = contact.localInfo?.messageField?.chatTime ?? 0;
    return chatTime;
  }

  /**
   * 创建PagingKey的便捷方法
   */
  private createPagingKey(contact: ContactEntity): PagingKey {
    return {
      sortValue: this.calculateSortValue(contact),
      id: contact.id
    };
  }

  /**
   * 加载混合数据（置顶 + 分割线 + 普通联系人 + 虚拟联系人）
   * 用于INIT和无key的REFRESH场景
   */
  private async loadMixedData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 1. 加载置顶联系人（全部Tab不应用筛选条件）
      const topSql = this.allContactDao!.buildAllContactTopSql();
      const topContacts = await this.contactDao!.rawQueryContacts(topSql, []);
      Logger.debug(TAG, `loaded ${topContacts.length} top contacts for all contacts tab`);

      // 缓存置顶联系人
      this.topContactsCache = topContacts;

      // 2. 构建混合数据列表
      const mixedData: ContactEntity[] = [];

      // 添加置顶联系人
      if (topContacts.length > 0) {
        if (topContacts.length <= 4) {
          // ≤4个置顶：显示所有置顶
          mixedData.push(...topContacts);
        } else {
          // >4个置顶：根据展开状态决定显示内容
          if (this.isTopExpanded) {
            mixedData.push(...topContacts);
          } else {
            // 只显示有未读的置顶联系人
            const unreadTopContacts = topContacts.filter(c => c.localInfo.noneReadCount > 0);
            mixedData.push(...unreadTopContacts);
          }
        }

        // 添加置顶分割线
        const divider = this.createTopDivider(topContacts.length);
        mixedData.push(divider);
      }

      // 3. 加载普通联系人（排除置顶联系人）
      const normalSql = this.allContactDao!.buildAllContactNonTopSql();
      const normalContacts = await this.contactDao!.rawQueryContacts(normalSql, [querySize]);
      Logger.debug(TAG, `loaded ${normalContacts.length} normal contacts for all contacts tab`);


      // 检查是否还有更多数据
      const hasMoreNormalData = normalContacts.length >= querySize;

      // 4. 使用统一的混合逻辑处理虚拟联系人
      const combinedContacts =
        await this.mixVirtualContactsForPagePosition(normalContacts, LoadDirection.INIT, hasMoreNormalData, querySize);

      // 添加混合后的联系人到数据列表，并在适当位置插入WeekAge分割线
      const finalContacts = await this.insertWeekAgeDivider(combinedContacts);
      mixedData.push(...finalContacts);

      const virtualCount =
        combinedContacts.filter(c => VirtualContactManager.getInstance().isVirtualContact(c.friendId)).length;
      Logger.debug(TAG,
        `mixed data final result: ${mixedData.length} items (including ${virtualCount} virtual contacts), hasMore=${hasMoreNormalData}`);

      // 5. 生成分页键
      const prevKey = null; // 初始加载没有prevKey
      const nextKey = hasMoreNormalData && combinedContacts.length > 0
        ? this.createPagingKey(combinedContacts[combinedContacts.length - 1])
        : null;

      const page: LoadResultPage<PagingKey, ContactEntity> = {
        type: 'PAGE',
        data: mixedData,
        prevKey: prevKey,
        nextKey: nextKey,
      };
      return page;
    } catch (e) {
      Logger.error(TAG, `loadMixedData failed: ${e}`);
      return {
        type: 'ERROR',
        error: e instanceof Error ? e : new Error('Unknown error during mixed data loading')
      };
    }
  }

  /**
   * 创建置顶分割线对象
   */
  private createTopDivider(topCount: number): ContactEntity {
    const divider = new ContactEntity();

    if (topCount > 4) {
      divider.friendId = F2FriendType.TopDivider; // 可折叠分割线
    } else {
      divider.friendId = F2FriendType.NonFoldTopDivider; // 简单分割线
    }

    divider.name = "折叠置顶聊天";
    divider.localInfo.friendDefaultAvatarIndex = topCount;

    // 计算未读置顶联系人数量
    const unreadTopCount = this.topContactsCache.filter(c => c.localInfo.noneReadCount > 0).length;
    divider.localInfo.noneReadCount = unreadTopCount;

    return divider;
  }

  /**
   * 在联系人列表中插入WeekAge分割线（用于初始加载）
   * 找到第一个一周前的联系人位置并插入分割线
   */
  private async insertWeekAgeDivider(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0) {
      return contacts;
    }

    // 找到第一个一周前的联系人位置
    let weekAgeIndex = -1;
    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i];
      if (ContactUtils.isWeekAgeGray(contact)) {
        weekAgeIndex = i;
        break;
      }
    }

    // 如果找到了一周前的联系人，插入分割线
    if (weekAgeIndex > -1) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, weekAgeIndex);
      result.splice(weekAgeIndex, 0, weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at index ${weekAgeIndex}`);
      return result;
    }

    return contacts;
  }

  /**
   * 在APPEND加载时检查是否需要插入WeekAge分割线
   * 这个方法处理分页加载过程中跨越一周边界的情况
   */
  private async insertWeekAgeDividerIfNeeded(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted) {
      return contacts;
    }

    // 检查是否有一周前的联系人
    let hasWeekAgeContact = false;
    for (const contact of contacts) {
      if (ContactUtils.isWeekAgeGray(contact)) {
        hasWeekAgeContact = true;
        break;
      }
    }

    // 如果有一周前的联系人，在第一个位置插入分割线
    // 因为这是APPEND加载，分割线应该在这批数据的开头
    if (hasWeekAgeContact) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, 0);
      result.unshift(weekAgeDivider); // 插入到开头
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at beginning during APPEND load`);
      return result;
    }

    return contacts;
  }

  /**
   * 创建WeekAge分割线对象
   */
  private async createWeekAgeDivider(contacts: ContactEntity[], insertIndex: number): Promise<ContactEntity> {
    const divider = new ContactEntity();
    divider.friendId = F2FriendType.WeekAge;
    divider.name = "一周之前消息";
    divider.localInfo.avatarIndex = insertIndex;

    // 🔥 使用SQL查询获取所有一周前联系人的准确未读数量
    try {
      await this.ensureDaoInitialized();
      const totalUnreadCount = await this.allContactDao!.queryAllContactWeekAgeUnreadCount();
      divider.localInfo.noneReadCount = totalUnreadCount;
      Logger.debug(TAG, `WeekAge divider unread count from SQL: ${totalUnreadCount}`);
    } catch (error) {
      Logger.error(TAG, `Failed to query WeekAge unread count: ${error}`);
      // 降级处理：使用当前可见数据计算
      let unreadCount = 0;
      for (let i = insertIndex; i < contacts.length; i++) {
        const contact = contacts[i];
        if (ContactUtils.isWeekAgeGray(contact) && contact.localInfo.noneReadCount > 0) {
          unreadCount += contact.localInfo.noneReadCount;
        }
      }
      divider.localInfo.noneReadCount = unreadCount;
    }

    return divider;
  }

  /**
   * 获取虚拟联系人（功能抽屉）
   * 通过VirtualContactManager统一管理
   */
  private async getVirtualContacts(): Promise<ContactEntity[]> {
    try {
      const virtualContacts = await VirtualContactManager.getInstance().getVirtualContacts();
      Logger.debug(TAG, `Retrieved ${virtualContacts.length} virtual contacts from VirtualContactManager`);
      return virtualContacts;
    } catch (error) {
      Logger.error(TAG, `Failed to get virtual contacts: ${error}`);
      return [];
    }
  }


  /**
   * 根据页面位置混合虚拟联系人
   * 基于当前页面是第一页、最后一页还是中间页来决定虚拟联系人的插入策略
   */
  private async mixVirtualContactsForPagePosition(realContacts: ContactEntity[], direction: LoadDirection,
    hasMoreData: boolean, querySize: number): Promise<ContactEntity[]> {
    const virtualContacts = await this.getVirtualContacts();
    if (virtualContacts.length === 0) {
      return realContacts;
    }

    // 如果没有真实联系人，只在INIT/REFRESH时返回虚拟联系人
    if (realContacts.length === 0) {
      if (direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) {
        return virtualContacts.slice(0, querySize);
      }
      return realContacts;
    }

    // 获取真实联系人的时间范围
    const times = realContacts.map(c => c.localInfo?.messageField?.chatTime || 0);
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const currentTime = Date.now();


    const isOnlyOnePage = (direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) && !hasMoreData;

    // 判断页面位置
    const isFirstPage = direction === LoadDirection.INIT || direction === LoadDirection.REFRESH ||
      (direction === LoadDirection.PREPEND && !hasMoreData);
    const isLastPage = direction === LoadDirection.APPEND && !hasMoreData;
    const isMiddlePage = !isFirstPage && !isLastPage;

    // 根据页面位置过滤虚拟联系人
    let relevantVirtual: ContactEntity[] = [];

    if (isOnlyOnePage) {
      relevantVirtual = virtualContacts
    } else if (isFirstPage) {
      // 第一页：需要小于当前时间的，大于等于当前最小时间，小于等于当前最大时间的
      relevantVirtual = virtualContacts.filter(vc => {
        const vcTime = vc.localInfo?.messageField?.chatTime || 0;
        return vcTime < currentTime && vcTime >= minTime && vcTime <= maxTime;
      });
      Logger.debug(TAG,
        `First page: filtering virtual contacts with time < ${currentTime} && >= ${minTime} && <= ${maxTime}`);

    } else if (isMiddlePage) {
      // 中间页：需要大于等于当前最小时间，小于等于当前最大时间的
      relevantVirtual = virtualContacts.filter(vc => {
        const vcTime = vc.localInfo?.messageField?.chatTime || 0;
        return vcTime >= minTime && vcTime <= maxTime;
      });
      Logger.debug(TAG, `Middle page: filtering virtual contacts with time >= ${minTime} && <= ${maxTime}`);

    } else if (isLastPage) {
      // 最后一页：需要大于等于当前最小时间的
      relevantVirtual = virtualContacts.filter(vc => {
        const vcTime = vc.localInfo?.messageField?.chatTime || 0;
        return vcTime >= minTime;
      });
      Logger.debug(TAG, `Last page: filtering virtual contacts with time >= ${minTime}`);
    }

    if (relevantVirtual.length === 0) {
      return realContacts;
    }

    // 合并并按时间排序
    const allContacts = [...realContacts, ...relevantVirtual];
    allContacts.sort((a, b) => {
      const timeA = a.localInfo?.messageField?.chatTime || 0;
      const timeB = b.localInfo?.messageField?.chatTime || 0;

      if (timeA !== timeB) {
        return timeB - timeA; // 降序
      }

      // 时间相同时，虚拟联系人排在真实联系人后面
      const isVirtualA = VirtualContactManager.getInstance().isVirtualContact(a.friendId);
      const isVirtualB = VirtualContactManager.getInstance().isVirtualContact(b.friendId);

      if (isVirtualA !== isVirtualB) {
        return isVirtualA ? 1 : -1;
      }

      // 都是相同类型时，按ID降序
      return (b.id || 0) - (a.id || 0);
    });

    // 对于INIT/REFRESH，确保返回数量不超过querySize
    if ((direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) &&
      allContacts.length > querySize) {
      allContacts.splice(querySize);
    }

    const insertedVirtualCount = allContacts.filter(c =>
    VirtualContactManager.getInstance().isVirtualContact(c.friendId) && relevantVirtual.includes(c)
    ).length;

    const pageType = isFirstPage ? 'first' : isLastPage ? 'last' : 'middle';
    Logger.debug(TAG,
      `Mixed ${insertedVirtualCount} virtual contacts for ${pageType} page, total: ${allContacts.length}`);
    return allContacts;
  }
}
