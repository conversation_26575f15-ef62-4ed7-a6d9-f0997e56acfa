import { PagingDataAdapter } from './PagingDataAdapter';
import { PagingSource, PagingConfig } from './PagingSource';
import { Pager } from './Pager';
import { OnlyContactPagingSource, PagingKey } from './OnlyContactPagingSource';
import { AllContactPagingSource } from './AllContactPagingSource';
import { NewGreetingPagingSource, RecommendSortType } from './NewGreetingPagingSource';
import { HaveInterviewPagingSource } from './HaveInterviewPagingSource';
import { ExchangeMessagePagingSource } from './ExchangeMessagePagingSource';
import { ContactEntity, GroupType } from '../db/entity/ContactEntity';
import { ContactDataItemIdentifier } from './ContactDataItemIdentifier';
import { DataSourceRegistry } from './invalidation/DataSourceRegistry';
import { InvalidationStrategyType } from './invalidation/DataInvalidationTypes';
import { Logger } from 'lib_zputils/Index';
import { UnreadStatisticsManager, UnreadStatistics } from './UnreadStatisticsManager';


const TAG = 'ContactPagingRegistry';

/**
 * 联系人分页注册表 (单例)
 *
 * 经过重构，这个类现在专门负责创建和管理“仅沟通”分组的PagingDataAdapter。
 * 它确保在整个应用中只有一个适配器实例，从而保持状态统一。
 */
export class ContactPagingRegistry {
  private static instance: ContactPagingRegistry;

  // 多Tab适配器管理
  private adapters: Map<GroupType, PagingDataAdapter<PagingKey, ContactEntity>> = new Map();
  private pagingSources: Map<GroupType, PagingSource<PagingKey, ContactEntity>> = new Map();
  
  // 统一的置顶展开状态控制
  private globalTopExpanded: boolean = true;
  
  // 统一的筛选状态控制
  private globalFilterType: number = 0;
  private globalJobId: number = 0;

  // Tab标识常量
  public static readonly TAB_ONLY_CONTACT = 'only-contact';
  public static readonly TAB_ALL_CONTACT = 'all-contact';
  public static readonly TAB_NEW_GREETING = 'new-greeting';
  public static readonly TAB_HAVE_INTERVIEW = 'have-interview';
  public static readonly TAB_EXCHANGE_MESSAGE = 'exchange-message';

  // 私有构造函数，防止外部直接实例化
  private constructor() {}

  /**
   * 获取注册表的单例实例
   */
  public static getInstance(): ContactPagingRegistry {
    if (!ContactPagingRegistry.instance) {
      ContactPagingRegistry.instance = new ContactPagingRegistry();
    }
    return ContactPagingRegistry.instance;
  }

  /**
   * 通用的适配器创建方法
   */
  private async createAndCacheAdapter(tabId: GroupType): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    if (this.adapters.has(tabId)) {
      return this.adapters.get(tabId)!;
    }

    const config: PagingConfig = {
      prefetchDistance: 30,        // 距离边界30项时开始预加载
      initialLoadSize: 30,         // 初始加载30项
      pageSize: 30,               // 每页加载30项
      enablePlaceholders: false,   // 不启用占位符
      maxSize: 200                // 最大缓存200项，超过时智能清除
    };

    // 根据tabId创建对应的PagingSource
    let pagingSource: PagingSource<PagingKey, ContactEntity>;
    let sourceRegistryId: string;

    try {
      if (tabId === GroupType.ONLY_CONTACT) {
        pagingSource = new OnlyContactPagingSource();
        sourceRegistryId = 'only-contacts';
      } else if (tabId === GroupType.ALL) {
        pagingSource = new AllContactPagingSource();
        sourceRegistryId = 'all-contacts';
      } else if (tabId === GroupType.NEW_GREETING) {
        pagingSource = new NewGreetingPagingSource();
        sourceRegistryId = 'new-greeting-contacts';
      } else if (tabId === GroupType.HAVE_INTERVIEW) {
        pagingSource = new HaveInterviewPagingSource();
        sourceRegistryId = 'have-interview-contacts';
      } else if (tabId === GroupType.EXCHANGE_MESSAGE) {
        pagingSource = new ExchangeMessagePagingSource();
        sourceRegistryId = 'exchange-message-contacts';
      } else {
        throw new Error(`Unsupported tab ID: ${tabId}`);
      }
    } catch (error) {
      Logger.error(TAG, `Failed to create PagingSource for tab ${tabId}: ${error}`);
      throw new Error(`PagingSource creation failed: ${error}`);
    }

    // 设置统一的置顶状态

    pagingSource.setTopExpanded(this.globalTopExpanded);

    // 设置统一的筛选状态
    pagingSource.setFilter(this.globalFilterType, this.globalJobId);


    // 缓存PagingSource
    this.pagingSources.set(tabId, pagingSource);

    const pager = new Pager<PagingKey, ContactEntity>(
      config,
      () => this.pagingSources.get(tabId)!
    );

    // 创建联系人数据标识符，用于占位对象感知更新
    const contactIdentifier = new ContactDataItemIdentifier();

    const adapter = new PagingDataAdapter<PagingKey, ContactEntity>(
      pager,
      contactIdentifier
    );

    // 注册到数据源注册表，启用自动失效功能
    DataSourceRegistry.getInstance().register(sourceRegistryId, adapter, {
      strategy: InvalidationStrategyType.VISIBLE_RANGE,
      retainPageCount: 1,           // 保留当前页±1页
      debounceDelay: 500,           // 500ms防抖
      enablePositionPreservation: true,
      maxRetainSize: 200,
      pageSize: config.pageSize     // 使用实际的页面大小配置
    });

    // 缓存适配器
    this.adapters.set(tabId, adapter);

    Logger.debug(TAG, `Created and cached adapter for tab: ${tabId} with diff optimization and auto-invalidation enabled.`);
    return adapter;
  }

  /**
   * 创建并缓存“仅沟通”分组的适配器（保持向后兼容）
   */
  private async createAndCacheOnlyContactAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.ONLY_CONTACT);
  }

  /**
   * 获取“仅沟通”分组的 PagingDataAdapter 实例（保持向后兼容）
   */
  public async getOnlyContactAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.ONLY_CONTACT);
  }

  /**
   * 获取"仅沟通"分组的 PagingSource 实例（保持向后兼容）
   * 用于UI层直接访问PagingSource的方法（如设置置顶展开状态）
   */
  public getOnlyContactPagingSource(): OnlyContactPagingSource | null {
    const source = this.pagingSources.get(GroupType.ONLY_CONTACT);
    return source instanceof OnlyContactPagingSource ? source : null;
  }







  /**
   * 获取"新招呼"分组的 PagingDataAdapter 实例
   */
  public async getNewGreetingAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.NEW_GREETING);
  }

  /**
   * 获取"新招呼"分组的 PagingSource 实例
   */
  public getNewGreetingPagingSource(): NewGreetingPagingSource | null {
    const source = this.pagingSources.get(GroupType.NEW_GREETING);
    return source instanceof NewGreetingPagingSource ? source : null;
  }



  /**
   * 设置"新招呼"分组的推荐排序类型
   */
  public setNewGreetingSortType(sortType: RecommendSortType): void {
    const source = this.getNewGreetingPagingSource();
    if (source) {
      source.setSortType(sortType);
    }
  }

  /**
   * 获取"新招呼"分组的推荐排序类型
   */
  public getNewGreetingSortType(): RecommendSortType {
    const source = this.getNewGreetingPagingSource();
    return source ? source.getSortType() : RecommendSortType.NONE;
  }

  /**
   * 通用方法：获取指定Tab的PagingDataAdapter实例
   */
  public async getAdapter(tabId: GroupType): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(tabId);
  }

  /**
   * 获取"全部"分组的 PagingDataAdapter 实例
   */
  public async getAllContactAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.ALL);
  }

  /**
   * 获取指定Tab的PagingSource实例
   */
  public getPagingSource(tabId: GroupType): PagingSource<PagingKey, ContactEntity> | null {
    return this.pagingSources.get(tabId) || null;
  }

  /**
   * 获取"全部"分组的 PagingSource 实例
   */
  public getAllContactPagingSource(): AllContactPagingSource | null {
    const source = this.pagingSources.get(GroupType.ALL);
    return source instanceof AllContactPagingSource ? source : null;
  }

  /**
   * 统一设置所有Tab的置顶展开状态
   */
  public setGlobalTopExpanded(expanded: boolean): void {
    this.globalTopExpanded = expanded;
    
    // 更新所有已创建的PagingSource的置顶状态
    this.pagingSources.forEach((source) => {
        source.setTopExpanded(expanded);
    });
    
    Logger.debug(TAG, `Global top expanded state set to: ${expanded}`);
  }

  /**
   * 获取全局置顶展开状态
   */
  public getGlobalTopExpanded(): boolean {
    return this.globalTopExpanded;
  }

  /**
   * 统一设置所有Tab的筛选条件
   */
  public setGlobalFilter(filterType: number, jobId?: number): void {
    this.globalFilterType = filterType;
    this.globalJobId = jobId || 0;
    
    // 更新所有已创建的PagingSource的筛选状态
    this.pagingSources.forEach((source) => {
        source.setFilter(filterType, jobId);
    });
    
    Logger.debug(TAG, `Global filter state set to: filterType=${filterType}, jobId=${jobId}`);
  }

  /**
   * 获取全局筛选类型
   */
  public getGlobalFilterType(): number {
    return this.globalFilterType;
  }

  /**
   * 获取全局职位ID
   */
  public getGlobalJobId(): number {
    return this.globalJobId;
  }

  /**
   * 获取指定分组的未读统计
   */
  public async getUnreadStatistics(groupType: GroupType): Promise<UnreadStatistics> {
    return await UnreadStatisticsManager.getInstance().getUnreadStatistics(groupType);
  }

  /**
   * 获取所有分组的未读统计
   */
  public async getAllUnreadStatistics(): Promise<Map<GroupType, UnreadStatistics>> {
    return await UnreadStatisticsManager.getInstance().getAllUnreadStatistics();
  }

  /**
   * 刷新未读统计数据
   */
  public async refreshUnreadStatistics(): Promise<void> {
    await UnreadStatisticsManager.getInstance().refreshAllStatistics();
  }



  /**
   * 获取"有面试"分组的 PagingDataAdapter 实例
   */
  public async getHaveInterviewAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.HAVE_INTERVIEW);
  }

  /**
   * 获取"有面试"分组的 PagingSource 实例
   */
  public getHaveInterviewPagingSource(): HaveInterviewPagingSource | null {
    const source = this.pagingSources.get(GroupType.HAVE_INTERVIEW);
    return source instanceof HaveInterviewPagingSource ? source : null;
  }

  /**
   * 设置"有面试"分组的筛选条件（保持向后兼容，实际调用统一方法）
   * @deprecated 请使用 setGlobalFilter() 方法
   */
  public setHaveInterviewFilter(filterType: number, jobId?: number): void {
    this.setGlobalFilter(filterType, jobId);
  }



  /**
   * 获取"有交换"分组的 PagingDataAdapter 实例
   */
  public async getExchangeMessageAdapter(): Promise<PagingDataAdapter<PagingKey, ContactEntity>> {
    return await this.createAndCacheAdapter(GroupType.EXCHANGE_MESSAGE);
  }

  /**
   * 获取"有交换"分组的 PagingSource 实例
   */
  public getExchangeMessagePagingSource(): ExchangeMessagePagingSource | null {
    const source = this.pagingSources.get(GroupType.EXCHANGE_MESSAGE);
    return source instanceof ExchangeMessagePagingSource ? source : null;
  }

  /**
   * 设置"有交换"分组的筛选条件（保持向后兼容，实际调用统一方法）
   * @deprecated 请使用 setGlobalFilter() 方法
   */
  public setExchangeMessageFilter(filterType: number, jobId?: number): void {
    this.setGlobalFilter(filterType, jobId);
  }

  /**
   * 销毁指定Tab的适配器
   */
  public destroyTab(tabId: GroupType): void {
    const adapter = this.adapters.get(tabId);
    if (adapter) {
      // 确定注册表ID
      let sourceRegistryId: string;
      if (tabId === GroupType.ONLY_CONTACT) {
        sourceRegistryId = 'only-contacts';
      } else if (tabId === GroupType.ALL) {
        sourceRegistryId = 'all-contacts';
      } else if (tabId === GroupType.NEW_GREETING) {
        sourceRegistryId = 'new-greeting-contacts';
      } else if (tabId === GroupType.HAVE_INTERVIEW) {
        sourceRegistryId = 'have-interview-contacts';
      } else if (tabId === GroupType.EXCHANGE_MESSAGE) {
        sourceRegistryId = 'exchange-message-contacts';
      } else {
        sourceRegistryId = ""; // 使用tabId作为默认值
      }

      // 从数据源注册表中注销
      DataSourceRegistry.getInstance().unregister(sourceRegistryId);

      adapter.destroy();
      this.adapters.delete(tabId);
      this.pagingSources.delete(tabId);

      Logger.debug(TAG, `Tab ${tabId} adapter has been destroyed and unregistered.`);
    }
  }

  /**
   * 销毁适配器并清空注册表。
   */
  public destroy(): void {
    // 销毁所有Tab
    const tabIds = Array.from(this.adapters.keys());
    for (const tabId of tabIds) {
      this.destroyTab(tabId);
    }

    // 清空Map
    this.adapters.clear();
    this.pagingSources.clear();

    Logger.debug(TAG, 'All adapters have been destroyed and unregistered.');
  }
}
