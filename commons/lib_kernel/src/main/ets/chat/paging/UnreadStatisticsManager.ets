import { ContactDao } from '../db/dao/ContactDao';
import { OnlyContactDao } from '../db/dao/OnlyContactDao';
import { NewGreetingDao } from '../db/dao/NewGreetingDao';
import { HaveInterviewDao } from '../db/dao/HaveInterviewDao';
import { ExchangeMessageDao } from '../db/dao/ExchangeMessageDao';
import { GroupType } from '../db/entity/ContactEntity';
import { ContactPagingRegistry } from './ContactPagingRegistry';
import { Kernel } from '../../Kernel';
import { Logger } from 'lib_zputils/Index';
import { ZPEvents } from 'lib_zputils/Index';

const TAG = 'UnreadStatisticsManager';

/**
 * 未读统计数据接口
 */
export interface UnreadStatistics {
  unreadContactCount: number;  // 有未读消息的联系人数量
  unreadMessageCount: number;  // 未读消息总数
}

/**
 * 未读统计管理器
 * 负责统一管理各个分组的未读数量统计
 */
export class UnreadStatisticsManager {
  private static instance: UnreadStatisticsManager;
  
  // 缓存的统计数据
  private cachedStats: Map<GroupType, UnreadStatistics> = new Map();
  
  // DAO实例
  private contactDao: ContactDao | null = null;
  private onlyContactDao?: OnlyContactDao;
  private newGreetingDao?: NewGreetingDao;
  private haveInterviewDao?: HaveInterviewDao;
  private exchangeMessageDao?: ExchangeMessageDao;
  
  // 监听器列表
  private listeners: Array<(allStats: Map<GroupType, UnreadStatistics>) => void> = [];
  
  // 更新锁，防止并发更新
  private updating: boolean = false;
  
  public static getInstance(): UnreadStatisticsManager {
    if (!UnreadStatisticsManager.instance) {
      UnreadStatisticsManager.instance = new UnreadStatisticsManager();
      UnreadStatisticsManager.instance.initialize();
    }
    return UnreadStatisticsManager.instance;
  }
  
  /**
   * 初始化：注册数据变化监听
   */
  private initialize(): void {
    // 监听联系人数据变化事件
    getContext().getApplicationContext().eventHub.on(ZPEvents.Contact.SyncAll, () => {
      this.refreshAllStatistics();
    });
  }
  
  /**
   * 初始化DAO实例
   */
  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao) {
      this.contactDao = await Kernel.getInstance().dbService().getContactDao();
      if (!this.contactDao) {
        throw new Error('ContactDao initialization failed');
      }
      
      this.onlyContactDao = new OnlyContactDao(this.contactDao);
      this.newGreetingDao = new NewGreetingDao(this.contactDao);
      this.haveInterviewDao = new HaveInterviewDao(this.contactDao);
      this.exchangeMessageDao = new ExchangeMessageDao(this.contactDao);
    }
  }
  
  /**
   * 获取指定分组的统计数据
   */
  public async getUnreadStatistics(groupType: GroupType): Promise<UnreadStatistics> {
    if (this.cachedStats.has(groupType)) {
      return this.cachedStats.get(groupType)!;
    }
    
    // 缓存未命中，刷新所有统计数据
    await this.refreshAllStatistics();
    
    return this.cachedStats.get(groupType) || { unreadContactCount: 0, unreadMessageCount: 0 };
  }
  
  /**
   * 获取所有分组的统计数据
   */
  public async getAllUnreadStatistics(): Promise<Map<GroupType, UnreadStatistics>> {
    if (this.cachedStats.size === 0) {
      await this.refreshAllStatistics();
    }
    return new Map(this.cachedStats);
  }
  
  /**
   * 刷新所有分组的统计数据
   */
  public async refreshAllStatistics(): Promise<void> {
    if (this.updating) {
      return; // 如果正在更新，直接返回
    }
    
    this.updating = true;
    
    try {
      await this.ensureDaoInitialized();
      
      const registry = ContactPagingRegistry.getInstance();
      const filterType = registry.getGlobalFilterType();
      const jobId = registry.getGlobalJobId();
      
      // 并行查询所有分组的统计数据

      const onlyContactStats = await this.queryOnlyContactStats(filterType, jobId)
      const newGreetingStats = await this.queryNewGreetingStats(filterType, jobId)
      const haveInterviewStats = await this.queryHaveInterviewStats(filterType, jobId)
      const exchangeMessageStats = await this.queryExchangeMessageStats(filterType, jobId)
      
      // 计算全部分组的统计
      const allStats: UnreadStatistics = {
        unreadContactCount: onlyContactStats.unreadContactCount + newGreetingStats.unreadContactCount + 
                           haveInterviewStats.unreadContactCount + exchangeMessageStats.unreadContactCount,
        unreadMessageCount: onlyContactStats.unreadMessageCount + newGreetingStats.unreadMessageCount + 
                           haveInterviewStats.unreadMessageCount + exchangeMessageStats.unreadMessageCount
      };
      
      // 更新缓存
      this.cachedStats.set(GroupType.ONLY_CONTACT, onlyContactStats);
      this.cachedStats.set(GroupType.NEW_GREETING, newGreetingStats);
      this.cachedStats.set(GroupType.HAVE_INTERVIEW, haveInterviewStats);
      this.cachedStats.set(GroupType.EXCHANGE_MESSAGE, exchangeMessageStats);
      this.cachedStats.set(GroupType.ALL, allStats);
      
      Logger.debug(TAG, `Refreshed all statistics: ALL(${allStats.unreadContactCount}/${allStats.unreadMessageCount})`);
      
      // 通知所有监听器
      this.notifyListeners();
      
    } catch (error) {
      Logger.error(TAG, `Failed to refresh statistics: ${error}`);
    } finally {
      this.updating = false;
    }
  }
  
  /**
   * 查询仅沟通分组的未读统计
   */
  private async queryOnlyContactStats(filterType: number, jobId: number): Promise<UnreadStatistics> {
    try {
      const unreadContactSql = this.onlyContactDao!.buildOnlyContactNonTopSql(filterType, jobId)
        .replace('SELECT *', 'SELECT COUNT(*) as count')
        .replace('ORDER BY local_chatTime DESC, id DESC LIMIT ?', '')
        .replace('LIMIT ?', '') + ' AND local_noneReadCount > 0';
      
      const unreadMessageSql = this.onlyContactDao!.buildOnlyContactNonTopSql(filterType, jobId)
        .replace('SELECT *', 'SELECT SUM(local_noneReadCount) as total')
        .replace('ORDER BY local_chatTime DESC, id DESC LIMIT ?', '')
        .replace('LIMIT ?', '') + ' AND local_noneReadCount > 0';
      const contactCount : number = await this.executeCountQuery(unreadContactSql);
      const messageCount : number = await this.executeCountQuery(unreadMessageSql);
      return { unreadContactCount: contactCount, unreadMessageCount: messageCount };
    } catch (error) {
      Logger.error(TAG, `Failed to query only contact stats: ${error}`);
      return { unreadContactCount: 0, unreadMessageCount: 0 };
    }
  }
  
  /**
   * 查询新招呼分组的未读统计
   */
  private async queryNewGreetingStats(filterType: number, jobId: number): Promise<UnreadStatistics> {
    try {
      // 查询有未读消息的联系人数量
      const unreadContactSql = this.newGreetingDao!.buildNewGreetingContactSql(filterType, jobId)
        .replace('SELECT *', 'SELECT COUNT(*) as count')
        .replace('ORDER BY local_chatTime DESC, id DESC', '') + ' AND local_noneReadCount > 0';
      
      const unreadContactCount = await this.executeCountQuery(unreadContactSql);
      const unreadMessageCount = await this.newGreetingDao!.queryNewGreetingTotalUnreadCount(filterType, jobId);
      
      return {
        unreadContactCount: unreadContactCount,
        unreadMessageCount: unreadMessageCount
      };
    } catch (error) {
      Logger.error(TAG, `Failed to query new greeting stats: ${error}`);
      return { unreadContactCount: 0, unreadMessageCount: 0 };
    }
  }
  
  /**
   * 查询有面试分组的未读统计
   */
  private async queryHaveInterviewStats(filterType: number, jobId: number): Promise<UnreadStatistics> {
    try {
      // 查询有未读消息的联系人数量
      const unreadContactSql = this.haveInterviewDao!.buildHaveInterviewContactSql(filterType, jobId)
        .replace('SELECT *', 'SELECT COUNT(*) as count')
        .replace('ORDER BY local_chatTime DESC, id DESC', '') + ' AND local_noneReadCount > 0';
      
      const unreadContactCount = await this.executeCountQuery(unreadContactSql);
      const unreadMessageCount = await this.haveInterviewDao!.queryHaveInterviewTotalUnreadCount(filterType, jobId);
      
      return {
        unreadContactCount: unreadContactCount,
        unreadMessageCount: unreadMessageCount
      };
    } catch (error) {
      Logger.error(TAG, `Failed to query have interview stats: ${error}`);
      return { unreadContactCount: 0, unreadMessageCount: 0 };
    }
  }
  
  /**
   * 查询有交换分组的未读统计
   */
  private async queryExchangeMessageStats(filterType: number, jobId: number): Promise<UnreadStatistics> {
    try {
      // 查询有未读消息的联系人数量
      const unreadContactSql = this.exchangeMessageDao!.buildExchangeMessageContactSql(filterType, jobId)
        .replace('SELECT *', 'SELECT COUNT(*) as count')
        .replace('ORDER BY local_chatTime DESC, id DESC', '') + ' AND local_noneReadCount > 0';
      
      const unreadContactCount = await this.executeCountQuery(unreadContactSql);
      const unreadMessageCount = await this.exchangeMessageDao!.queryExchangeMessageTotalUnreadCount(filterType, jobId);
      
      return {
        unreadContactCount: unreadContactCount,
        unreadMessageCount: unreadMessageCount
      };
    } catch (error) {
      Logger.error(TAG, `Failed to query exchange message stats: ${error}`);
      return { unreadContactCount: 0, unreadMessageCount: 0 };
    }
  }
  
  /**
   * 执行计数查询
   */
  private async executeCountQuery(sql: string): Promise<number> {
    try {
      const cursor = await this.contactDao!.rawQuery(sql, []);
      if (cursor && cursor.goToFirstRow()) {
        const count = cursor.getLong(cursor.getColumnIndex('count'));
        cursor.close();
        return count;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `Count query failed: ${error}`);
      return 0;
    }
  }
  
  /**
   * 执行求和查询
   */
  private async executeSumQuery(sql: string): Promise<number> {
    try {
      const cursor = await this.contactDao!.rawQuery(sql, []);
      if (cursor && cursor.goToFirstRow()) {
        const total = cursor.getLong(cursor.getColumnIndex('total'));
        cursor.close();
        return total || 0;
      }
      return 0;
    } catch (error) {
      Logger.error(TAG, `Sum query failed: ${error}`);
      return 0;
    }
  }
  
  /**
   * 添加统计数据变化监听器
   */
  public addListener(listener: (allStats: Map<GroupType, UnreadStatistics>) => void): void {
    this.listeners.push(listener);
  }
  
  /**
   * 移除监听器
   */
  public removeListener(listener: (allStats: Map<GroupType, UnreadStatistics>) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    const allStats = new Map(this.cachedStats);
    this.listeners.forEach(listener => {
      try {
        listener(allStats);
      } catch (error) {
        Logger.error(TAG, `Listener error: ${error}`);
      }
    });
  }
  
  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.cachedStats.clear();
  }
}