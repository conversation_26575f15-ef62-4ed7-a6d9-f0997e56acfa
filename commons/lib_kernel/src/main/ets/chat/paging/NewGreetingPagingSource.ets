import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactDao } from '../db/dao/ContactDao';
import { NewGreetingDao } from '../db/dao/NewGreetingDao';
import { Kernel } from '../../Kernel';
import { Logger, DateUtils, TextUtils, LList, CheckUtils } from 'lib_zputils/Index';
import { F2FriendType } from '../f2/F2ContactManager';
import { PagingSource, LoadParams, LoadResult, LoadResultPage, PagingState, LoadDirection, Page } from './PagingSource';
import { ContactDataItemIdentifier } from './ContactDataItemIdentifier';
import { ContactUtils } from '../ContactUtils';
import { GreetSortListResponse } from '../http/GreetSortListResponse';
import { ChatUrlConfig, POST, HttpResponse, ZPHttpError } from 'lib_http/Index';
import ContactClassManager from '../ContactClassManager';
import { ContactSortManager } from '../f2/ContactSortManager';
import { CommonConfigManager } from '../../config/custom/CommonConfigManager';
import NotifyUtils from '../NotifyUtils';


const TAG = 'NewGreetingPagingSource';

/**
 * 分页键，用于数据库查询定位
 * - sortValue: 主要排序依据（如时间戳）
 * - id: 次要排序依据，用于处理sortValue相同时的排序稳定性
 */
export interface PagingKey {
  sortValue: number;

  id?: number;
}

/**
 * 推荐排序类型枚举
 */
export enum RecommendSortType {
  NONE = 0, // 无排序
  RECOMMEND = 1, // 推荐排序
  TIME = 2 // 时间排序
}

/**
 * 新招呼分组的分页数据源实现。
 * 继承自通用的 PagingSource，负责从数据库加载新招呼联系人数据。
 * 支持推荐排序功能。
 */
interface RecommendSortBean {
  contact: ContactEntity;

  sortIndex: number;
}

export class NewGreetingPagingSource extends PagingSource<PagingKey, ContactEntity> {
  private contactDao: ContactDao | null = null;

  private newGreetingDao: NewGreetingDao | null = null;

  // WeekAge分割线相关状态
  private weekAgeDividerInserted: boolean = false;


  // 🔥 推荐排序相关状态
  private sortType: RecommendSortType = RecommendSortType.NONE;

  private recommendSortData: ContactEntity[] = []; // API返回的推荐排序结果
  private recommendSortTime: number = -1; // 推荐排序的时间戳
  private recommendSortCache: Map<string, number> = new Map(); // 推荐排序的缓存：contactId -> sortIndex

  // 🐌 网络延迟模拟配置
  private static ENABLE_SLOW_SIMULATION = false;

  private static SLOW_DELAY_MS = 2000;

  private static RANDOM_DELAY = true;

  constructor() {
    super();
    // 初始化时检查推荐排序配置
    this.initRecommendSortType();
  }

  /**
   * 初始化推荐排序类型
   */
  private initRecommendSortType(): void {
    if (this.isSupportRecommendSort()) {
      this.sortType = CommonConfigManager.getInstance().getGreetingRecSort() as RecommendSortType;
    }
  }

  /**
   * 判断是否支持推荐排序
   */
  private isSupportRecommendSort(): boolean {
    return NotifyUtils.isNewContactCommon() && CommonConfigManager.getInstance().getGreetingRecSort() > 0;
  }

  /**
   * 🐌 模拟网络延迟
   */
  private async simulateNetworkDelay(): Promise<void> {
    if (!NewGreetingPagingSource.ENABLE_SLOW_SIMULATION) {
      return;
    }

    let delay = NewGreetingPagingSource.SLOW_DELAY_MS;
    if (NewGreetingPagingSource.RANDOM_DELAY) {
      const randomFactor = 0.5 + Math.random();
      delay = Math.floor(delay * randomFactor);
    }

    Logger.debug(TAG, `🐌 模拟网络延迟: ${delay}ms`);
    return new Promise<void>((resolve) => {
      setTimeout(() => resolve(), delay);
    });
  }

  /**
   * 设置推荐排序类型
   */
  public setSortType(sortType: RecommendSortType): void {
    if (this.sortType !== sortType) {
      this.sortType = sortType;
      // 状态改变时清除缓存
      this.clearRecommendSortCache();
    }
  }

  /**
   * 获取推荐排序类型
   */
  public getSortType(): RecommendSortType {
    return this.sortType;
  }

  /**
   * 清除推荐排序缓存
   */
  private clearRecommendSortCache(): void {
    this.recommendSortData = [];
    this.recommendSortTime = -1;
    this.recommendSortCache.clear();
  }

  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao || !this.newGreetingDao) {
      this.contactDao = await Kernel.getInstance().dbService().getContactDao();
      if (!this.contactDao) {
        throw new Error('ContactDao initialization failed');
      }

      this.newGreetingDao = new NewGreetingDao(this.contactDao);
      Logger.debug(TAG, 'ContactDao and NewGreetingDao initialized successfully');
    }
  }

  /**
   * 获取推荐排序的联系人ID列表（用于API调用）
   */
  private async getRecommendSortContactIds(): Promise<Array<Record<string, Object>> | null> {
    try {
      await this.ensureDaoInitialized();

      // 查询新招呼联系人
      const sql = this.newGreetingDao!.buildNewGreetingContactSql(this.filterType, this.jobId);
      const contacts = await this.contactDao!.rawQueryContacts(sql, [100]); // 限制100个

      if (contacts.length < 5) {
        Logger.debug(TAG, `recommend sort: insufficient contacts (${contacts.length} < 5)`);
        return null;
      }

      // 按时间排序
      ContactSortManager.executeDefaultSort(contacts);

      const mapList: Array<Record<string, Object>> = [];
      for (const contact of contacts) {
        // 跳过7天内的联系人
        if (DateUtils.isSameWeek(ContactClassManager.getShowTime(contact))) {
          continue;
        }

        // 需要有securityId
        if (TextUtils.isEmpty(contact.securityId)) {
          continue;
        }

        mapList.push({
          "securityId": contact.securityId,
          "unread": ContactClassManager.getUnreadCount(contact) > 0
        });

        if (mapList.length >= 100) {
          break;
        }
      }

      if (mapList.length < 5) {
        Logger.debug(TAG, `recommend sort: insufficient valid contacts (${mapList.length} < 5)`);
        return null;
      }

      Logger.debug(TAG, `recommend sort: prepared ${mapList.length} contacts for API`);
      return mapList;

    } catch (error) {
      Logger.error(TAG, `getRecommendSortContactIds failed: ${error}`);
      return null;
    }
  }

  /**
   * 调用推荐排序API
   */
  private async fetchRecommendSortData(): Promise<boolean> {
    try {
      const contactIds = await this.getRecommendSortContactIds();
      if (!contactIds) {
        return false;
      }

      const jsonObject: Record<string, Object> = { "geekList": contactIds };

      const response = await POST<GreetSortListResponse>(ChatUrlConfig.URL_ZPRELATION_BOSS_FRIEND_GREET_SORT_LIST, {
        scene: 1,
        geekInfos: JSON.stringify(jsonObject),
      });

      if (response.zpData && response.zpData.sortedList) {
        this.recommendSortData = response.zpData.sortedList;
        this.recommendSortTime = Date.now();
        this.buildRecommendSortCache();

        Logger.debug(TAG, `recommend sort API success: ${this.recommendSortData.length} items`);
        return true;
      }

      return false;

    } catch (error) {
      Logger.error(TAG, `fetchRecommendSortData failed: ${error}`);
      return false;
    }
  }

  /**
   * 构建推荐排序缓存
   */
  private buildRecommendSortCache(): void {
    this.recommendSortCache.clear();

    for (let i = 0; i < this.recommendSortData.length; i++) {
      const contact = this.recommendSortData[i];
      const contactId = `${contact.friendId}_${contact.friendSource}`;
      this.recommendSortCache.set(contactId, i);
    }

    Logger.debug(TAG, `built recommend sort cache: ${this.recommendSortCache.size} items`);
  }

  /**
   * 应用推荐排序到联系人列表
   * 增强版：支持新增数据的智能处理，更接近无分页版本的效果
   */
  private applyRecommendSort(contacts: ContactEntity[]): ContactEntity[] {
    if (this.sortType !== RecommendSortType.RECOMMEND || this.recommendSortCache.size === 0) {
      return contacts;
    }

    // 分离有推荐排序和无推荐排序的联系人
    const sortedWithIndex: Array<RecommendSortBean> = [];
    const unsortedContacts: ContactEntity[] = [];
    const newContacts: ContactEntity[] = []; // 新增的联系人（推荐排序后新出现的）

    for (const contact of contacts) {
      const contactId = `${contact.friendId}_${contact.friendSource}`;
      const sortIndex = this.recommendSortCache.get(contactId);

      if (sortIndex !== undefined) {
        sortedWithIndex.push({ contact, sortIndex });
      } else {
        // 检查是否是新增的联系人（推荐排序时间之后的联系人）
        const contactTime = contact.localInfo?.messageField?.chatTime || 0;
        if (this.recommendSortTime > 0 && contactTime > this.recommendSortTime) {
          newContacts.push(contact);
        } else {
          unsortedContacts.push(contact);
        }
      }
    }

    // 按推荐排序索引排序
    sortedWithIndex.sort((a, b) => a.sortIndex - b.sortIndex);
    const sortedByRecommend = sortedWithIndex.map(item => item.contact);

    // 新增联系人按时间排序
    ContactSortManager.executeDefaultSort(newContacts);

    // 合并结果：新增联系人在最前面，然后是推荐排序的，最后是未排序的
    // 这样更接近无分页版本的效果
    const result = [...newContacts, ...sortedByRecommend, ...unsortedContacts];

    Logger.debug(TAG,
      `applied recommend sort: ${newContacts.length} new, ${sortedByRecommend.length} sorted, ${unsortedContacts.length} unsorted`);
    return result;
  }

  /**
   * 核心加载方法。根据提供的分页键从数据库加载新招呼数据。
   */
  async load(params: LoadParams<PagingKey>): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      await this.ensureDaoInitialized();
      Logger.debug(TAG, `🚀 开始加载新招呼数据: ${JSON.stringify(params)}`);

      // 🐌 模拟网络延迟
      await this.simulateNetworkDelay();

      const key = params.key;
      const loadSize = params.loadSize;
      const direction = params.direction;

      let sql: string;
      let sqlParams: (string | number | boolean | null)[];

      // 使用LIMIT + 1技巧来准确判断是否还有更多数据
      const querySize = loadSize + 1;

      // 根据加载方向选择不同的SQL构建方法
      switch (direction) {
        case LoadDirection.INIT:
          // 🔥 INIT时重置状态并加载数据
          this.weekAgeDividerInserted = false;
          return await this.loadInitialData(querySize);
        case LoadDirection.REFRESH:
          if (!key) {
            // 🔥 REFRESH无key时重置状态并加载数据
            this.weekAgeDividerInserted = false;
            return await this.loadInitialData(querySize);
          }
          sql = this.newGreetingDao!.buildNewGreetingAppendSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.APPEND:
          if (!key) {
            throw new Error('Key is required for APPEND load');
          }
          sql = this.newGreetingDao!.buildNewGreetingAppendSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.PREPEND:
          if (!key) {
            throw new Error('Key is required for PREPEND load');
          }
          sql = this.newGreetingDao!.buildNewGreetingPrependSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        default:
          throw new Error(`Unsupported load direction: ${direction}`);
      }

      Logger.debug(TAG, `Executing SQL: ${sql} with params: ${JSON.stringify(sqlParams)}`);

      // 执行查询
      let contacts = await this.contactDao?.rawQueryContacts(sql, sqlParams);
      if (!contacts) {
        contacts = [];
      }

      Logger.debug(TAG, `raw query returned ${contacts.length} contacts (requested ${querySize})`);

      // 使用LIMIT + 1技巧判断是否还有更多数据
      let hasMoreData = false;
      if (contacts.length > loadSize) {
        hasMoreData = true;
        contacts = contacts.slice(0, loadSize);
        Logger.debug(TAG, `has more data, trimmed to ${contacts.length} contacts`);
      } else {
        hasMoreData = false;
        Logger.debug(TAG, `reached end of pagination, returned ${contacts.length} contacts`);
      }

      // PREPEND查询的特殊处理
      if (direction === LoadDirection.PREPEND) {
        if (!hasMoreData) {
          return await this.loadInitialData(querySize);
        }
        contacts.reverse();
      }

      // 🔥 在APPEND加载时检查是否需要插入WeekAge分割线
      if (direction === LoadDirection.APPEND && !this.weekAgeDividerInserted) {
        contacts = await this.insertWeekAgeDividerIfNeeded(contacts);
      }

      // 🔥 应用推荐排序
      if (this.sortType === RecommendSortType.RECOMMEND && contacts.length > 0) {
        contacts = this.applyRecommendSort(contacts);
      }

      Logger.debug(TAG, `final result: ${contacts.length} contacts, hasMore=${hasMoreData}`);

      const prevKey = this.getPrevKey(contacts, direction, hasMoreData);
      const nextKey = this.getNextKey(contacts, direction, hasMoreData);

      const page: LoadResultPage<PagingKey, ContactEntity> = {
        type: 'PAGE',
        data: contacts,
        prevKey: prevKey,
        nextKey: nextKey,
      };
      return page;
    } catch (e) {
      Logger.error(TAG, `load failed: ${e}`);
      return {
        type: 'ERROR',
        error: e instanceof Error ? e : new Error('Unknown error during data loading')
      };
    }
  }

  /**
   * 加载初始数据（用于INIT和无key的REFRESH场景）
   */
  private async loadInitialData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 🔥 推荐排序时不支持置顶功能，直接使用原有逻辑
      if (this.sortType === RecommendSortType.RECOMMEND) {
        return await this.loadRecommendSortData(querySize);
      }

      // 🔥 非推荐排序时支持置顶功能
      return await this.loadMixedData(querySize);

    } catch (e) {
      Logger.error(TAG, `loadInitialData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 加载推荐排序数据（不支持置顶）
   */
  private async loadRecommendSortData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 1. 🔥 如果启用推荐排序，先获取推荐排序数据
      const sortSuccess = await this.fetchRecommendSortData();
      Logger.debug(TAG, `recommend sort fetch result: ${sortSuccess}`);

      // 2. 加载新招呼联系人
      const sql = this.newGreetingDao!.buildNewGreetingContactSql(this.filterType, this.jobId);
      let contacts = await this.contactDao!.rawQueryContacts(sql, [querySize]);
      Logger.debug(TAG,
        `loaded ${contacts.length} new greeting contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 3. 🔥 应用推荐排序
      if (contacts.length > 0) {
        contacts = this.applyRecommendSort(contacts);
        Logger.debug(TAG, `applied recommend sort to ${contacts.length} contacts`);
      }

      // 4. 插入WeekAge分割线
      const finalContacts = await this.insertWeekAgeDivider(contacts);

      // 5. 添加排序选项（如果支持推荐排序）
      const mixedData: ContactEntity[] = [];
      if (this.isSupportRecommendSort() && !Kernel.getInstance().isGeekRole()) {
        const sortOption = this.createSortOption();
        mixedData.push(sortOption);
      }
      mixedData.push(...finalContacts);

      Logger.debug(TAG, `recommend sort data: ${mixedData.length} total items`);

      // 6. 构建分页键
      const prevKey = null;
      const nextKey = mixedData.length > 0 ? this.createPagingKey(mixedData[mixedData.length - 1]) : null;

      return {
        type: 'PAGE',
        data: mixedData,
        prevKey: prevKey,
        nextKey: nextKey
      };

    } catch (e) {
      Logger.error(TAG, `loadRecommendSortData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 加载混合数据（置顶 + 分割线 + 普通联系人）
   * 用于非推荐排序的INIT和无key的REFRESH场景
   */
  private async loadMixedData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 1. 加载置顶联系人（应用筛选条件）
      const topSql = this.newGreetingDao!.buildNewGreetingTopContactSql(this.filterType, this.jobId);
      const topContacts = await this.contactDao!.rawQueryContacts(topSql, []);
      Logger.debug(TAG, `loaded ${topContacts.length} top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 缓存置顶联系人
      this.topContactsCache = topContacts;

      // 2. 加载普通联系人（排除置顶，应用筛选条件）
      const nonTopSql = this.newGreetingDao!.buildNewGreetingContactSql(this.filterType, this.jobId);
      const nonTopContacts = await this.contactDao!.rawQueryContacts(nonTopSql, [querySize]);
      Logger.debug(TAG, `loaded ${nonTopContacts.length} non-top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 3. 构建混合数据
      const mixedData: ContactEntity[] = [];

      // 添加排序选项（如果支持推荐排序）
      if (this.isSupportRecommendSort() && !Kernel.getInstance().isGeekRole()) {
        const sortOption = this.createSortOption();
        mixedData.push(sortOption);
      }

      // 添加置顶联系人
      if (topContacts.length > 0) {
        if (topContacts.length <= 4) {
          // ≤4个置顶：显示所有置顶
          mixedData.push(...topContacts);
        } else {
          // >4个置顶：根据展开状态决定显示内容
          if (this.isTopExpanded) {
            mixedData.push(...topContacts);
          } else {
            // 只显示有未读的置顶联系人
            const unreadTopContacts = topContacts.filter(c => c.localInfo.noneReadCount > 0);
            mixedData.push(...unreadTopContacts);
          }
        }

        // 添加置顶分割线
        const divider = this.createTopDivider(topContacts.length);
        mixedData.push(divider);
      }

      // 添加普通联系人，并在适当位置插入WeekAge分割线
      const finalContacts = await this.insertWeekAgeDivider(nonTopContacts);
      mixedData.push(...finalContacts);

      Logger.debug(TAG, `mixed data: ${topContacts.length} top + dividers + ${nonTopContacts.length} normal = ${mixedData.length} total`);

      // 4. 构建分页键
      const prevKey = null;
      const nextKey = mixedData.length > 0 ? this.createPagingKey(mixedData[mixedData.length - 1]) : null;

      return {
        type: 'PAGE',
        data: mixedData,
        prevKey: prevKey,
        nextKey: nextKey
      };

    } catch (e) {
      Logger.error(TAG, `loadMixedData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 创建置顶分割线对象
   */
  private createTopDivider(topCount: number): ContactEntity {
    const divider = new ContactEntity();

    if (topCount > 4) {
      divider.friendId = F2FriendType.TopDivider;  // 可折叠分割线
    } else {
      divider.friendId = F2FriendType.NonFoldTopDivider;  // 简单分割线
    }

    divider.name = "折叠置顶聊天";
    divider.localInfo.friendDefaultAvatarIndex = topCount;

    // 计算未读置顶联系人数量
    const unreadTopCount = this.topContactsCache.filter(c => c.localInfo.noneReadCount > 0).length;
    divider.localInfo.noneReadCount = unreadTopCount;

    return divider;
  }

  /**
   * 创建排序选项联系人
   */
  private createSortOption(): ContactEntity {
    const sortOption = new ContactEntity();
    sortOption.friendId = F2FriendType.NewHelloSort;
    sortOption.name = "新招呼排序选项";
    sortOption.addTime = this.recommendSortTime;
    sortOption.localInfo.noneReadType = this.sortType;
    return sortOption;
  }

  /**
   * 生成上一页的键
   */
  private getPrevKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    if (direction === LoadDirection.PREPEND && !hasMoreData) {
      Logger.debug(TAG, 'PREPEND reached beginning, no more newer data');
      return null;
    }

    if (direction === LoadDirection.INIT) {
      return null;
    }

    const keyItem = items[0];
    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  /**
   * 生成下一页的键
   */
  private getNextKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }
    // 使用LIMIT + 1技巧的结果来准确判断是否还有更多数据
    // 对于APPEND方向，如果没有更多数据，说明已经到达最旧数据的边界
    if ((direction === LoadDirection.APPEND || direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) && !hasMoreData) {
      Logger.debug(TAG, 'APPEND reached end, no more older data');
      return null;
    }

    const keyItem = items[items.length - 1];
    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  private calculateSortValue(contact: ContactEntity): number {
    const chatTime = contact.localInfo?.messageField?.chatTime ?? 0;
    return chatTime;
  }

  /**
   * 创建PagingKey的便捷方法
   */
  private createPagingKey(contact: ContactEntity): PagingKey {
    return {
      sortValue: this.calculateSortValue(contact),
      id: contact.id
    };
  }

  /**
   * 获取用于刷新的键
   */
  getRefreshKey(state: PagingState<PagingKey, ContactEntity> | null): PagingKey | null {
    // 简化实现：返回null表示从头开始刷新
    return null;
  }

  /**
   * 获取新招呼联系人总数
   */
  async getAllCount(): Promise<number> {
    try {
      await this.ensureDaoInitialized();
      return await this.newGreetingDao!.queryNewGreetingContactCount(this.filterType, this.jobId);
    } catch (e) {
      Logger.error(TAG, `getAllCount failed: ${e}`);
      return 0;
    }
  }

  /**
   * 在联系人列表中插入WeekAge分割线（用于初始加载）
   */
  private async insertWeekAgeDivider(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 找到第一个一周前的联系人位置
    let weekAgeIndex = -1;
    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i];
      if (ContactUtils.isWeekAgeGray(contact)) {
        weekAgeIndex = i;
        break;
      }
    }

    // 如果找到了一周前的联系人，插入分割线
    if (weekAgeIndex > -1) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, weekAgeIndex);
      result.splice(weekAgeIndex, 0, weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at index ${weekAgeIndex}`);
      return result;
    }

    return contacts;
  }

  /**
   * 在APPEND加载时检查是否需要插入WeekAge分割线
   */
  private async insertWeekAgeDividerIfNeeded(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 检查是否有一周前的联系人
    let hasWeekAgeContact = false;
    for (const contact of contacts) {
      if (ContactUtils.isWeekAgeGray(contact)) {
        hasWeekAgeContact = true;
        break;
      }
    }

    // 如果有一周前的联系人，在第一个位置插入分割线
    if (hasWeekAgeContact) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, 0);
      result.unshift(weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at beginning during APPEND load`);
      return result;
    }

    return contacts;
  }

  /**
   * 创建WeekAge分割线对象
   */
  private async createWeekAgeDivider(contacts: ContactEntity[], insertIndex: number): Promise<ContactEntity> {
    const divider = new ContactEntity();
    divider.friendId = F2FriendType.WeekAge;
    divider.name = "一周之前消息";
    divider.localInfo.avatarIndex = insertIndex;

    // 计算未读数量
    let unreadCount = 0;
    for (let i = insertIndex; i < contacts.length; i++) {
      const contact = contacts[i];
      if (ContactUtils.isWeekAgeGray(contact) && contact.localInfo.noneReadCount > 0) {
        unreadCount += contact.localInfo.noneReadCount;
      }
    }
    divider.localInfo.noneReadCount = unreadCount;

    return divider;
  }
}