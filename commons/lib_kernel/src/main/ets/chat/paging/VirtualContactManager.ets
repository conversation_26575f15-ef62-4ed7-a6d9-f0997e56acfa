import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactDao } from '../db/dao/ContactDao';
import { Kernel } from '../../Kernel';
import { Logger } from 'lib_zputils/Index';
import { MqttConfig } from '../MqttConfig';
import { BossPositionRequestSettingManager } from '../BossPositionRequestSettingManager';
import { GeekPositionRequestSettingManager } from '../GeekPositionRequestSettingManager';
import F2TipBarManager from '../f2/F2TipBarManager';
import { NunReadType } from '../db/entity/ContactLocalInfo';
import { common } from '@kit.AbilityKit';
import { ZPEvents } from 'lib_zputils/Index';
import { ContactParam } from '../db/entity/ContactParam';

const TAG = 'VirtualContactManager';

/**
 * 虚拟联系人管理器
 * 统一管理所有虚拟联系人（功能抽屉）的创建和显示逻辑
 * 通过SQL查询替代对F2ContactManager的依赖
 */
export class VirtualContactManager {
  private static instance: VirtualContactManager;
  private contactDao: ContactDao | null = null;
  private eventHub: common.EventHub | null = null;
  private isListening: boolean = false;
  
  // 缓存相关 - 仅用于快速查询，不设置过期时间
  private virtualContactsCache: ContactEntity[] = [];
  
  // 数据变更回调
  private changeCallbacks: Set<() => void> = new Set();

  // 虚拟联系人ID列表（用于快速判断）
  private static VIRTUAL_CONTACT_IDS = [
    MqttConfig.CHAT_POSITION_REQUEST_SETTING,
    MqttConfig.CHAT_CATCH_BACK_CHAT,
    MqttConfig.CHAT_UNFIT,
    MqttConfig.CHAT_INFORMITY_GEEK,
    MqttConfig.CHAT_811_VIP_HIGH_GEEK
  ];

  private constructor() {}

  public static getInstance(): VirtualContactManager {
    if (!VirtualContactManager.instance) {
      VirtualContactManager.instance = new VirtualContactManager();
    }
    return VirtualContactManager.instance;
  }

  /**
   * 开始监听数据变化事件
   */
  public startListening(eventHub: common.EventHub): void {
    if (this.isListening) {
      Logger.warn(TAG, 'Already listening to virtual contact changes');
      return;
    }
    
    this.eventHub = eventHub;
    this.isListening = true;
    
    // 监听联系人同步事件
    this.eventHub.on(ZPEvents.Contact.SyncAll, this.handleContactSyncEvent.bind(this));
    
    Logger.info(TAG, 'Started listening to virtual contact changes');
  }
  
  /**
   * 停止监听数据变化事件
   */
  public stopListening(): void {
    if (!this.isListening || !this.eventHub) {
      return;
    }
    
    this.eventHub.off(ZPEvents.Contact.SyncAll, this.handleContactSyncEvent.bind(this));
    
    this.eventHub = null;
    this.isListening = false;
    
    Logger.info(TAG, 'Stopped listening to virtual contact changes');
  }
  
  /**
   * 注册数据变更回调
   */
  public registerChangeCallback(callback: () => void): void {
    this.changeCallbacks.add(callback);
  }
  
  /**
   * 移除数据变更回调
   */
  public unregisterChangeCallback(callback: () => void): void {
    this.changeCallbacks.delete(callback);
  }
  
  /**
   * 处理联系人同步事件
   */
  private async handleContactSyncEvent(param: ContactParam): Promise<void> {
    Logger.debug(TAG, `Contact sync event received: friendId=${param.friendId}`);
    await this.refreshVirtualContacts();
  }

  /**
   * 刷新虚拟联系人数据
   */
  private async refreshVirtualContacts(): Promise<void> {
    // 清除缓存
    this.virtualContactsCache = [];
    
    // 重新获取数据
    await this.getVirtualContacts();
    
    // 通知所有回调
    this.notifyChangeCallbacks();
  }
  
  /**
   * 通知所有注册的回调
   */
  private notifyChangeCallbacks(): void {
    this.changeCallbacks.forEach((callback: () => void) => {
      try {
        callback();
      } catch (error) {
        Logger.error(TAG, `Error in change callback: ${error}`);
      }
    });
  }

    /**
   * 初始化DAO
   */
  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao) {
      this.contactDao = await Kernel.getInstance().dbService().getContactDao();
    }
  }

  /**
   * 执行计数查询的辅助方法
   */
  private async executeCountQuery(sql: string, params: Array<string | number | boolean | null | undefined> = []): Promise<number> {
    const cursor = await this.contactDao!.rawQuery(sql, params);
    try {
      if (cursor && cursor.goToFirstRow()) {
        return cursor.getLong(cursor.getColumnIndex('count'));
      }
      return 0;
    } finally {
      cursor?.close();
    }
  }

  /**
   * 判断是否为虚拟联系人
   */
  public isVirtualContact(friendId: number): boolean {
    return VirtualContactManager.VIRTUAL_CONTACT_IDS.includes(friendId);
  }

  /**
   * 获取所有虚拟联系人
   * 使用内存缓存，通过事件监听实时更新
   */
  public async getVirtualContacts(): Promise<ContactEntity[]> {
    // 如果有缓存直接返回
    if (this.virtualContactsCache.length > 0) {
      return this.virtualContactsCache;
    }

    try {
      await this.ensureDaoInitialized();
      
      const virtualContacts: ContactEntity[] = [];

      const vipUnfit = await this.createVIPUnfit();
      if (vipUnfit) virtualContacts.push(vipUnfit);
      
      // 根据用户身份获取相应的虚拟联系人
      if (Kernel.getInstance().isBossRole()) {
        // Boss身份的虚拟联系人
        const positionRequest = await this.createPositionRequestForBoss();
        if (positionRequest) virtualContacts.push(positionRequest);
        
        const batchBackChat = await this.createBatchBackChat();
        if (batchBackChat) virtualContacts.push(batchBackChat);
        
        const bossUnfit = await this.createBossUnfit();
        if (bossUnfit) virtualContacts.push(bossUnfit);
      }
      
      if (Kernel.getInstance().isGeekRole()) {
        // Geek身份的虚拟联系人
        const positionRequest = await this.createPositionRequestForGeek();
        if (positionRequest) virtualContacts.push(positionRequest);
        
        const geekUnInterest = await this.createGeekUnInterest();
        if (geekUnInterest) virtualContacts.push(geekUnInterest);
      }
      
      // 更新缓存
      this.virtualContactsCache = virtualContacts;
      
      Logger.debug(TAG, `Retrieved ${virtualContacts.length} virtual contacts`);
      return virtualContacts;
    } catch (error) {
      Logger.error(TAG, `Failed to get virtual contacts: ${error}`);
      return [];
    }
  }

  /**
   * 创建职位推荐设置（Boss）
   */
  private async createPositionRequestForBoss(): Promise<ContactEntity | null> {
    const instance = BossPositionRequestSettingManager.getInstance();
    if (!instance.show) return null;

    const bean = new ContactEntity();
    bean.isTop = false;
    bean.localInfo.noneReadType = ContactEntity.NONE_READ_SILENT;
    bean.localInfo.avatarResource = $r('app.media.icon_vip_choose');
    bean.friendId = MqttConfig.CHAT_POSITION_REQUEST_SETTING;
    bean.name = instance.title;
    bean.localInfo.messageField.chatTime = instance.timeMillSecond || this.getVirtualContactTime(bean);
    bean.localInfo.updateTime = instance.timeMillSecond;
    bean.localInfo.messageField.summary = instance.desc;
    bean.setUnreadCount(instance.showNoneRead ? 1 : 0);
    
    return bean;
  }

  /**
   * 创建职位推荐设置（Geek）
   */
  private async createPositionRequestForGeek(): Promise<ContactEntity | null> {
    const instance = GeekPositionRequestSettingManager.getInstance();
    if (!instance.show) return null;

    const bean = new ContactEntity();
    bean.isTop = false;
    bean.friendId = MqttConfig.CHAT_POSITION_REQUEST_SETTING;
    bean.localInfo.avatarResource = $r('app.media.icon_vip_choose');
    bean.name = instance.title;
    bean.localInfo.messageField.chatTime = instance.timeMillSecond || this.getVirtualContactTime(bean);
    bean.localInfo.updateTime = instance.timeMillSecond;
    bean.localInfo.messageField.summary = instance.desc;
    bean.setUnreadCount(instance.showNoneRead ? 1 : 0);
    
    return bean;
  }

  /**
   * 创建批量追聊
   * 通过SQL查询替代F2ContactManager
   */
  private async createBatchBackChat(): Promise<ContactEntity | null> {
    try {
      // 查询批量追聊的联系人
      const sql = `
        SELECT * FROM Contact 
        WHERE status > -1 
          AND friendId > 1000 
          AND itemType = 43
          AND isRejectUser = 0
        ORDER BY local_chatTime DESC
      `;
      
      const batchChatBackList = await this.contactDao!.rawQueryContacts(sql, []);
      
      if (batchChatBackList.length === 0) return null;
      
      const bean = new ContactEntity();
      bean.isTop = false;
      bean.friendId = MqttConfig.CHAT_CATCH_BACK_CHAT;
      bean.localInfo.avatarResource = $r('app.media.ic_chat_back');
      bean.name = "批量追聊";
      
      // 使用第一个联系人的时间
      const firstContact = batchChatBackList[0];
      bean.localInfo.messageField.chatTime = firstContact.localInfo?.messageField?.chatTime || this.getVirtualContactTime(bean);
      bean.datetime = firstContact.datetime;
      
      bean.localInfo.messageField.summary = `${batchChatBackList.length}个牛人收藏在这里`;
      
      // 计算未读数
      let unreadCount = 0;
      for (const contact of batchChatBackList) {
        unreadCount += contact.localInfo?.noneReadCount || 0;
      }
      bean.localInfo.noneReadCount = unreadCount;
      
      return bean;
    } catch (error) {
      Logger.error(TAG, `Failed to create BatchBackChat: ${error}`);
      return null;
    }
  }

  /**
   * 创建不符合要求牛人（VIP过滤）
   */
  private async createVIPUnfit(): Promise<ContactEntity | null> {
    const canFilterFriend = F2TipBarManager.canFilterFriend;
    const filterSetTime = F2TipBarManager.firstFilterSetTime || 0;
    
    // 查询被过滤的联系人数量
    const unfitContactSql = `
      SELECT * as count FROM Contact
      WHERE status > -1 
        AND friendId > 1000 
        AND isFiltered = 1
        ORDER BY local_chatTime DESC
    `;
    
    const unfitContacts = await this.contactDao?.rawQueryContacts(unfitContactSql, []);
    if ((unfitContacts == null || unfitContacts.length == 0) && !canFilterFriend) {
      return null;
    }
    
    const bean = new ContactEntity();
    bean.name = Kernel.getInstance().isBossRole() ? "不符合要求牛人" : "不符合要求的Boss";
    bean.isTop = false;
    bean.localInfo.noneReadType = ContactEntity.NONE_READ_SILENT;
    bean.localInfo.avatarResource = $r('app.media.icon_vip_choose');
    bean.friendId = MqttConfig.CHAT_INFORMITY_GEEK;
    
    // 获取最新的过滤时间
    if (unfitContacts && unfitContacts.length > 0) {
      const unfitTime = unfitContacts[0].localInfo.messageField.chatTime;
      
      bean.localInfo.messageField.chatTime = Math.max(unfitTime, filterSetTime);
      bean.localInfo.updateTime = bean.localInfo.messageField.chatTime;
    } else {
      bean.localInfo.messageField.chatTime = filterSetTime || this.getVirtualContactTime(bean);
      bean.localInfo.updateTime = bean.localInfo.messageField.chatTime;
    }
    
    if (unfitContacts && unfitContacts.length > 0) {
      if (Kernel.getInstance().isBossRole()) {
        bean.localInfo.messageField.summary = `近30天过滤${unfitContacts.length}位不符合职位要求的牛人`;
      } else {
        bean.localInfo.messageField.summary = `近30天过滤${unfitContacts.length}位不符合职位要求的Boss`;
      }

      let unRead = 0;
      for (const contact of unfitContacts) {
        if (contact.localInfo.noneReadCount > 0) {
          unRead += contact.localInfo.noneReadCount;
        }
      }
      bean.localInfo.noneReadCount = unRead;
    } else {
      bean.localInfo.messageField.summary = "点击可设置过滤要求";
    }
    return bean;
  }

  /**
   * 创建不合适的牛人（Boss）
   */
  private async createBossUnfit(): Promise<ContactEntity | null> {
    const sql = `
      SELECT COUNT(*) as count FROM Contact 
      WHERE status > -1 
        AND friendId > 1000 
        AND isRejectUser = 1
    `;
    
    const count = await this.executeCountQuery(sql);
    
    if (count === 0) return null;
    
    const bean = new ContactEntity();
    bean.localInfo.messageField.chatTime = 0; // 固定设置为0（最旧）
    bean.datetime = 0;
    bean.name = "不合适的牛人";
    bean.isTop = false;
    bean.localInfo.noneReadType = NunReadType.SILENT;
    bean.localInfo.messageField.summary = `${count}个联系人`;
    bean.friendId = MqttConfig.CHAT_UNFIT;
    bean.localInfo.avatarResource = $r('app.media.ic_f2_unfit');
    
    return bean;
  }

  /**
   * 创建不感兴趣的BOSS（Geek）
   */
  private async createGeekUnInterest(): Promise<ContactEntity | null> {
    const sql = `
      SELECT COUNT(*) as count FROM Contact 
      WHERE status > -1 
        AND friendId > 1000 
        AND isRejectUser = 1
    `;
    
    const count = await this.executeCountQuery(sql);
    
    if (count === 0) return null;
    
    const bean = new ContactEntity();
    bean.localInfo.messageField.chatTime = 0; // 固定设置为0（最旧）
    bean.datetime = 0;
    bean.name = "不感兴趣的BOSS";
    bean.isTop = false;
    bean.localInfo.noneReadType = NunReadType.SILENT;
    bean.localInfo.messageField.summary = `${count}个联系人`;
    bean.friendId = MqttConfig.CHAT_UNFIT;
    bean.localInfo.avatarResource = $r('app.media.ic_f2_unfit');
    
    return bean;
  }

  /**
   * 获取虚拟联系人的默认显示时间
   */
  private getVirtualContactTime(contact: ContactEntity): number {
    switch (contact.friendId) {
      case MqttConfig.CHAT_CATCH_BACK_CHAT: // 批量追聊
        return Date.now() - 24 * 60 * 60 * 1000; // 1天前
      
      case MqttConfig.CHAT_UNFIT: // 不合适的牛人/不感兴趣的BOSS
        return 0; // 固定设置为0（最旧），与无分页版本保持一致
        
      case MqttConfig.CHAT_INFORMITY_GEEK: // 不符合要求
        return Date.now() - 24 * 60 * 60 * 1000; // 1天前
      
      case MqttConfig.CHAT_POSITION_REQUEST_SETTING: // 职位推荐设置
      case MqttConfig.CHAT_811_VIP_HIGH_GEEK: // VIP高级牛人
        return Date.now() - 3 * 24 * 60 * 60 * 1000; // 3天前
        
      default:
        return Date.now() - 2 * 24 * 60 * 60 * 1000; // 默认2天前
    }
  }

  /**
   * 清除缓存并通知更新
   */
  public async clearCacheAndNotify(): Promise<void> {
    await this.refreshVirtualContacts();
  }
  
  /**
   * 仅清除缓存（不通知）
   */
  public clearCache(): void {
    this.virtualContactsCache = [];
  }
}

/**
 * 初始化虚拟联系人监听
 * 在应用启动时调用
 */
export function initVirtualContactMonitoring(eventHub: common.EventHub): void {
  VirtualContactManager.getInstance().startListening(eventHub);
  Logger.info(TAG, 'Virtual contact monitoring initialized');
}

/**
 * 清理虚拟联系人监听
 * 在应用关闭时调用
 */
export function cleanupVirtualContactMonitoring(): void {
  VirtualContactManager.getInstance().stopListening();
  Logger.info(TAG, 'Virtual contact monitoring cleaned up');
} 