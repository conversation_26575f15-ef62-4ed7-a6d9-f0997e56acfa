import { ContactEntity } from '../db/entity/ContactEntity';
import { ContactDao } from '../db/dao/ContactDao';
import { ExchangeMessageDao } from '../db/dao/ExchangeMessageDao';
import { Kernel } from '../../Kernel';
import { Logger } from 'lib_zputils/Index';
import { F2FriendType } from '../f2/F2ContactManager';
import { PagingSource, LoadParams, LoadResult, LoadDirection, LoadResultPage, PagingState} from './PagingSource';
import { ContactDataItemIdentifier } from './ContactDataItemIdentifier';
import { ContactUtils } from '../ContactUtils';
import { PagingKey } from './NewGreetingPagingSource';


const TAG = 'ExchangeMessagePagingSource';

/**
 * 有交换信息分组的分页数据源实现。
 * 继承自通用的 PagingSource，负责从数据库加载有交换信息的联系人数据。
 */
export class ExchangeMessagePagingSource extends PagingSource<PagingKey, ContactEntity> {
  private contactDao: ContactDao | null = null;
  private exchangeMessageDao: ExchangeMessageDao | null = null;

  // WeekAge分割线相关状态
  private weekAgeDividerInserted: boolean = false;


  constructor() {
    super();
  }

  private async ensureDaoInitialized(): Promise<void> {
    if (!this.contactDao || !this.exchangeMessageDao) {
      this.contactDao = await Kernel.getInstance().dbService().getContactDao();
      if (!this.contactDao) {
        throw new Error('ContactDao initialization failed');
      }

      this.exchangeMessageDao = new ExchangeMessageDao(this.contactDao);
      Logger.debug(TAG, 'ContactDao and ExchangeMessageDao initialized successfully');
    }
  }

  /**
   * 核心加载方法。根据提供的分页键从数据库加载数据。
   */
  async load(params: LoadParams<PagingKey>): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      await this.ensureDaoInitialized();
      Logger.debug(TAG, `🚀 开始加载数据: ${JSON.stringify(params)}`);

      const key = params.key;
      const loadSize = params.loadSize;
      const direction = params.direction;

      let sql: string;
      let sqlParams: (string | number | boolean | null)[];

      // 使用LIMIT + 1技巧来准确判断是否还有更多数据
      const querySize = loadSize + 1;

      // 根据加载方向选择不同的SQL构建方法
      switch (direction) {
        case LoadDirection.INIT:
          // 🔥 INIT时重置状态并加载数据
          this.weekAgeDividerInserted = false;
          return await this.loadInitialData(querySize);
        case LoadDirection.REFRESH:
          if (!key) {
            // 🔥 REFRESH无key时重置状态并加载数据
            this.weekAgeDividerInserted = false;
            return await this.loadInitialData(querySize);
          }
          sql = this.exchangeMessageDao!.buildExchangeMessageAppendSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.APPEND:
          if (!key) throw new Error('Key is required for APPEND load');
          sql = this.exchangeMessageDao!.buildExchangeMessageAppendSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        case LoadDirection.PREPEND:
          if (!key) throw new Error('Key is required for PREPEND load');
          sql = this.exchangeMessageDao!.buildExchangeMessagePrependSql(this.filterType, this.jobId);
          sqlParams = [key.sortValue, key.sortValue, key.id ? key.id : 0, querySize];
          break;
        default:
          throw new Error(`Unsupported load direction: ${direction}`);
      }

      Logger.debug(TAG, `Executing SQL: ${sql} with params: ${JSON.stringify(sqlParams)}`);

      // 执行查询
      let contacts = await this.contactDao?.rawQueryContacts(sql, sqlParams);
      if (!contacts) {
        contacts = [];
      }

      Logger.debug(TAG, `raw query returned ${contacts.length} contacts (requested ${querySize})`);

      // 使用LIMIT + 1技巧判断是否还有更多数据
      let hasMoreData = false;
      if (contacts.length > loadSize) {
        hasMoreData = true;
        contacts = contacts.slice(0, loadSize);
        Logger.debug(TAG, `has more data, trimmed to ${contacts.length} contacts`);
      } else {
        hasMoreData = false;
        Logger.debug(TAG, `reached end of pagination, returned ${contacts.length} contacts`);
      }

      // PREPEND查询的特殊处理
      if (direction === LoadDirection.PREPEND) {
        if (!hasMoreData) {
          return await this.loadInitialData(querySize);
        }
        contacts.reverse();
      }

      // 🔥 在APPEND加载时检查是否需要插入WeekAge分割线
      if (direction === LoadDirection.APPEND && !this.weekAgeDividerInserted) {
        contacts = await this.insertWeekAgeDividerIfNeeded(contacts);
      }

      Logger.debug(TAG, `final result: ${contacts.length} contacts, hasMore=${hasMoreData}`);

      const prevKey = this.getPrevKey(contacts, direction, hasMoreData);
      const nextKey = this.getNextKey(contacts, direction, hasMoreData);

      const page: LoadResultPage<PagingKey, ContactEntity> = {
        type: 'PAGE',
        data: contacts,
        prevKey: prevKey,
        nextKey: nextKey,
      };
      return page;
    } catch (e) {
      Logger.error(TAG, `load failed: ${e}`);
      return {
        type: 'ERROR',
        error: e instanceof Error ? e : new Error('Unknown error during data loading')
      };
    }
  }

  /**
   * 确定上一页的键
   */
  private getPrevKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    if (direction === LoadDirection.PREPEND && !hasMoreData) {
      Logger.debug(TAG, 'PREPEND reached beginning, no more newer data');
      return null;
    }

    if (direction === LoadDirection.INIT) {
      return null;
    }

    let keyItem: ContactEntity;
    keyItem = items[0];

    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  /**
   * 确定下一页的键
   */
  private getNextKey(items: ContactEntity[], direction: LoadDirection, hasMoreData: boolean): PagingKey | null {
    if (items.length === 0) {
      return null;
    }

    // 使用LIMIT + 1技巧的结果来准确判断是否还有更多数据
    // 对于APPEND方向，如果没有更多数据，说明已经到达最旧数据的边界
    if ((direction === LoadDirection.APPEND || direction === LoadDirection.INIT || direction === LoadDirection.REFRESH) && !hasMoreData) {
      Logger.debug(TAG, 'APPEND reached end, no more older data');
      return null;
    }

    let keyItem: ContactEntity;
    keyItem = items[items.length - 1];
    return {
      sortValue: this.calculateSortValue(keyItem),
      id: keyItem.id
    };
  }

  private calculateSortValue(contact: ContactEntity): number {
    const chatTime = contact.localInfo?.messageField?.chatTime ?? 0;
    return chatTime;
  }

  /**
   * 创建PagingKey的便捷方法
   */
  private createPagingKey(contact: ContactEntity): PagingKey {
    return {
      sortValue: this.calculateSortValue(contact),
      id: contact.id
    };
  }

  /**
   * 提供初始刷新时的键
   */
  getRefreshKey(state: PagingState<PagingKey, ContactEntity> | null): PagingKey | null {
    return null;
  }

  async getAllCount(): Promise<number> {
    try {
      await this.ensureDaoInitialized();
      return await this.exchangeMessageDao!.queryExchangeMessageContactCount(this.filterType, this.jobId);
    } catch (e) {
      Logger.error(TAG, `getAllCount failed: ${e}`);
      return 0;
    }
  }

  /**
   * 加载初始数据（用于INIT和无key的REFRESH场景）
   */
  private async loadInitialData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      return await this.loadMixedData(querySize);
    } catch (e) {
      Logger.error(TAG, `loadInitialData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 加载混合数据（置顶 + 分割线 + 普通联系人）
   * 用于INIT和无key的REFRESH场景
   */
  private async loadMixedData(querySize: number): Promise<LoadResult<PagingKey, ContactEntity>> {
    try {
      // 1. 加载置顶联系人（应用筛选条件）
      const topSql = this.exchangeMessageDao!.buildExchangeMessageTopContactSql(this.filterType, this.jobId);
      const topContacts = await this.contactDao!.rawQueryContacts(topSql, []);
      Logger.debug(TAG, `loaded ${topContacts.length} top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 缓存置顶联系人
      this.topContactsCache = topContacts;

      // 2. 加载普通联系人（排除置顶，应用筛选条件）
      const nonTopSql = this.exchangeMessageDao!.buildExchangeMessageContactSql(this.filterType, this.jobId);
      const nonTopContacts = await this.contactDao!.rawQueryContacts(nonTopSql, [querySize]);
      Logger.debug(TAG, `loaded ${nonTopContacts.length} non-top contacts with filter: ${this.filterType}, jobId: ${this.jobId}`);

      // 3. 构建混合数据
      const mixedData: ContactEntity[] = [];

      // 添加置顶联系人
      if (topContacts.length > 0) {
        if (topContacts.length <= 4) {
          // ≤4个置顶：显示所有置顶
          mixedData.push(...topContacts);
        } else {
          // >4个置顶：根据展开状态决定显示内容
          if (this.isTopExpanded) {
            mixedData.push(...topContacts);
          } else {
            // 只显示有未读的置顶联系人
            const unreadTopContacts = topContacts.filter(c => c.localInfo.noneReadCount > 0);
            mixedData.push(...unreadTopContacts);
          }
        }

        // 添加置顶分割线
        const divider = this.createTopDivider(topContacts.length);
        mixedData.push(divider);
      }

      // 添加普通联系人，并在适当位置插入WeekAge分割线
      const finalContacts = await this.insertWeekAgeDivider(nonTopContacts);
      mixedData.push(...finalContacts);

      Logger.debug(TAG, `mixed data: ${topContacts.length} top + dividers + ${nonTopContacts.length} normal = ${mixedData.length} total`);

      // 4. 构建分页键
      const prevKey = null;
      const nextKey = mixedData.length > 0 ? this.createPagingKey(mixedData[mixedData.length - 1]) : null;

      return {
        type: 'PAGE',
        data: mixedData,
        prevKey: prevKey,
        nextKey: nextKey
      };

    } catch (e) {
      Logger.error(TAG, `loadMixedData failed: ${e}`);
      return { type: 'ERROR', error: e as Error };
    }
  }

  /**
   * 创建置顶分割线对象
   */
  private createTopDivider(topCount: number): ContactEntity {
    const divider = new ContactEntity();

    if (topCount > 4) {
      divider.friendId = F2FriendType.TopDivider;  // 可折叠分割线
    } else {
      divider.friendId = F2FriendType.NonFoldTopDivider;  // 简单分割线
    }

    divider.name = "折叠置顶聊天";
    divider.localInfo.friendDefaultAvatarIndex = topCount;

    // 计算未读置顶联系人数量
    const unreadTopCount = this.topContactsCache.filter(c => c.localInfo.noneReadCount > 0).length;
    divider.localInfo.noneReadCount = unreadTopCount;

    return divider;
  }

  /**
   * 在联系人列表中插入WeekAge分割线（用于初始加载）
   */
  private async insertWeekAgeDivider(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 找到第一个一周前的联系人位置
    let weekAgeIndex = -1;
    for (let i = 0; i < contacts.length; i++) {
      const contact = contacts[i];
      if (ContactUtils.isWeekAgeGray(contact)) {
        weekAgeIndex = i;
        break;
      }
    }

    // 如果找到了一周前的联系人，插入分割线
    if (weekAgeIndex > -1) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, weekAgeIndex);
      result.splice(weekAgeIndex, 0, weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at index ${weekAgeIndex}`);
      return result;
    }

    return contacts;
  }

  /**
   * 在APPEND加载时检查是否需要插入WeekAge分割线
   */
  private async insertWeekAgeDividerIfNeeded(contacts: ContactEntity[]): Promise<ContactEntity[]> {
    if (contacts.length === 0 || this.weekAgeDividerInserted || Kernel.getInstance().isBossRole()) {
      return contacts;
    }

    // 检查是否有一周前的联系人
    let hasWeekAgeContact = false;
    for (const contact of contacts) {
      if (ContactUtils.isWeekAgeGray(contact)) {
        hasWeekAgeContact = true;
        break;
      }
    }

    // 如果有一周前的联系人，在第一个位置插入分割线
    if (hasWeekAgeContact) {
      const result = [...contacts];
      const weekAgeDivider = await this.createWeekAgeDivider(contacts, 0);
      result.unshift(weekAgeDivider);
      this.weekAgeDividerInserted = true;
      Logger.debug(TAG, `inserted WeekAge divider at beginning during APPEND load`);
      return result;
    }

    return contacts;
  }

  /**
   * 创建WeekAge分割线对象
   */
  private async createWeekAgeDivider(contacts: ContactEntity[], insertIndex: number): Promise<ContactEntity> {
    const divider = new ContactEntity();
    divider.friendId = F2FriendType.WeekAge;
    divider.name = "一周之前消息";
    divider.localInfo.avatarIndex = insertIndex;

    // 🔥 使用SQL查询获取所有一周前联系人的准确未读数量
    try {
      await this.ensureDaoInitialized();
      const totalUnreadCount = await this.exchangeMessageDao!.queryExchangeMessageWeekAgeUnreadCount(this.filterType, this.jobId);
      divider.localInfo.noneReadCount = totalUnreadCount;
      Logger.debug(TAG, `WeekAge divider unread count from SQL: ${totalUnreadCount}`);
    } catch (error) {
      Logger.error(TAG, `Failed to query WeekAge unread count: ${error}`);
      // 降级处理：使用当前可见数据计算
      let unreadCount = 0;
      for (let i = insertIndex; i < contacts.length; i++) {
        const contact = contacts[i];
        if (ContactUtils.isWeekAgeGray(contact) && contact.localInfo.noneReadCount > 0) {
          unreadCount += contact.localInfo.noneReadCount;
        }
      }
      divider.localInfo.noneReadCount = unreadCount;
    }

    return divider;
  }
}