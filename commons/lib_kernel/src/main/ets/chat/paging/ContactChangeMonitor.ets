import { Logger } from 'lib_zputils/Index';
import { ZPEvents } from 'lib_zputils/Index';
import { ContactParam } from '../db/entity/ContactParam';
import { DataSourceRegistry } from './invalidation/DataSourceRegistry';
import { DataChangeEvent, ChangeType, ContactChangeMetadata } from './invalidation/DataInvalidationTypes';
import { common } from '@kit.AbilityKit';

const TAG = 'ContactChangeMonitor';

/**
 * 联系人数据变化监控器
 * 监听联系人数据库的变化，并通知数据源注册表进行相应的失效处理
 */
export class ContactChangeMonitor {
  private static instance: ContactChangeMonitor;
  private eventHub: common.EventHub | null = null;
  private isListening: boolean = false;
  
  private constructor() {}
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ContactChangeMonitor {
    if (!ContactChangeMonitor.instance) {
      ContactChangeMonitor.instance = new ContactChangeMonitor();
    }
    return ContactChangeMonitor.instance;
  }
  
  /**
   * 开始监听联系人数据变化
   */
  public startListening(eventHub: common.EventHub): void {
    if (this.isListening) {
      Logger.warn(TAG, 'Already listening to contact changes');
      return;
    }
    
    this.eventHub = eventHub;
    this.isListening = true;
    
    // 监听联系人同步事件
    this.eventHub.on(ZPEvents.Contact.SyncAll, this.handleContactSyncEvent.bind(this));
    
    Logger.info(TAG, 'Started listening to contact changes');
  }
  
  /**
   * 停止监听联系人数据变化
   */
  public stopListening(): void {
    if (!this.isListening || !this.eventHub) {
      return;
    }
    
    this.eventHub.off(ZPEvents.Contact.SyncAll, this.handleContactSyncEvent.bind(this));
    
    this.eventHub = null;
    this.isListening = false;
    
    Logger.info(TAG, 'Stopped listening to contact changes');
  }
  
  /**
   * 手动触发联系人数据失效
   */
  public triggerInvalidation(sourceId: string, changeType: ChangeType = ChangeType.UPDATE): void {
    const event: DataChangeEvent = {
      source: sourceId,
      changeType: changeType,
      timestamp: Date.now()
    };
    
    DataSourceRegistry.getInstance().notifyDataChange(event);
    Logger.debug(TAG, `Manually triggered invalidation for ${sourceId}`);
  }
  
  /**
   * 处理联系人同步事件
   */
  private handleContactSyncEvent(param: ContactParam): void {
    try {
      Logger.debug(TAG, `Contact sync event: friendId=${param.friendId}, friendSource=${param.friendSource}, isDelete=${param.isDelete}`);
      
      // 确定变化类型
      let changeType: ChangeType;
      if (param.isDelete) {
        changeType = ChangeType.DELETE;
      } else if (param.exist) {
        changeType = ChangeType.UPDATE;
      } else {
        changeType = ChangeType.INSERT;
      }
      
      // 生成受影响的ID
      const affectedId = `${param.friendId}_${param.friendSource}`;
      
      // 创建联系人变化元数据
      const metadata: ContactChangeMetadata = {
        friendId: param.friendId,
        friendSource: param.friendSource,
        isContactSync: param.isContactSync,
        batchHandle: param.batchHandle
      };

      // 需要通知的所有数据源
      const dataSources = [
        'only-contacts',           // 仅沟通分组
        'all-contacts',            // 全部分组
        'new-greeting-contacts',   // 新招呼分组
        'have-interview-contacts', // 有面试分组
        'exchange-message-contacts' // 有交换分组
      ];

      // 通知所有相关数据源
      dataSources.forEach(sourceId => {
        const event: DataChangeEvent = {
          source: sourceId,
          changeType: changeType,
          affectedIds: [affectedId],
          metadata: metadata,
          timestamp: Date.now()
        };
        
        DataSourceRegistry.getInstance().notifyDataChange(event);
      });
      
      Logger.debug(TAG, `Notified data change: ${changeType} for contact ${affectedId} to ${dataSources.length} data sources`);
      
    } catch (error) {
      Logger.error(TAG, `Error handling contact sync event: ${error}`);
    }
  }
  
  /**
   * 批量处理联系人变化
   */
  public handleBatchContactChange(contacts: ContactParam[]): void {
    if (contacts.length === 0) {
      return;
    }
    
    const affectedIds = contacts.map(param => `${param.friendId}_${param.friendSource}`);

    // 创建批量变化元数据
    const metadata: ContactChangeMetadata = {
      batchSize: contacts.length,
      contacts: contacts
    };

    // 需要通知的所有数据源
    const dataSources = [
      'only-contacts',           // 仅沟通分组
      'all-contacts',            // 全部分组
      'new-greeting-contacts',   // 新招呼分组
      'have-interview-contacts', // 有面试分组
      'exchange-message-contacts' // 有交换分组
    ];

    // 通知所有相关数据源
    dataSources.forEach(sourceId => {
      const event: DataChangeEvent = {
        source: sourceId,
        changeType: ChangeType.BATCH,
        affectedIds: affectedIds,
        metadata: metadata,
        timestamp: Date.now()
      };
      
      DataSourceRegistry.getInstance().notifyDataChange(event);
    });
    
    Logger.info(TAG, `Handled batch contact change: ${contacts.length} contacts to ${dataSources.length} data sources`);
  }
  
  /**
   * 获取监听状态
   */
  public isListeningToChanges(): boolean {
    return this.isListening;
  }
}

/**
 * 联系人变化监控器的便捷初始化函数
 * 在应用启动时调用，开始监听联系人数据变化
 */
export function initContactChangeMonitoring(eventHub: common.EventHub): void {
  ContactChangeMonitor.getInstance().startListening(eventHub);
  Logger.info(TAG, 'Contact change monitoring initialized');
}

/**
 * 清理联系人变化监控器
 * 在应用关闭时调用
 */
export function cleanupContactChangeMonitoring(): void {
  ContactChangeMonitor.getInstance().stopListening();
  Logger.info(TAG, 'Contact change monitoring cleaned up');
}
