# 虚拟联系人（功能抽屉）详细分析

## 概述

虚拟联系人是 Boss直聘 App 中的特殊功能入口，显示在联系人列表中，但并非真实的用户联系人。它们通过 `LocalModeImp` 类管理，根据用户身份（Boss/Geek）和特定条件动态显示。

## 虚拟联系人类型

### 1. 职位推荐设置 (PositionRequest)
- **ID**: `CHAT_POSITION_REQUEST_SETTING` = Number.MAX_SAFE_INTEGER - 17
- **适用身份**: Boss 和 Geek
- **图标**: `app.media.icon_vip_choose`
- **显示条件**:
  - Boss: `BossPositionRequestSettingManager.getInstance().show` 为 true
  - Geek: `GeekPositionRequestSettingManager.getInstance().show` 为 true
- **功能**: 职位推荐相关设置入口
- **未读消息**: 由对应 Manager 的 `showNoneRead` 控制

### 2. 批量追聊 (BatchBackChat) - Boss专属
- **ID**: `CHAT_CATCH_BACK_CHAT` = Number.MAX_SAFE_INTEGER - 21  
- **适用身份**: 仅 Boss
- **图标**: `app.media.ic_chat_back`
- **显示条件**: 有批量追聊的联系人（`batchChatBackList.length > 0`）
- **功能**: 管理批量追聊的牛人
- **摘要**: 显示收藏的牛人数量，如 "X个牛人收藏在这里"
- **时间**: 取第一个追聊联系人的聊天时间

### 3. 不符合要求牛人 (VIPUnfit) - Boss专属
- **ID**: `CHAT_INFORMITY_GEEK` = Number.MAX_SAFE_INTEGER - 8
- **适用身份**: 仅 Boss
- **图标**: `app.media.icon_vip_choose`
- **显示条件**: 
  - 有不符合要求的牛人（`unfitCount > 0`）
  - 或可以设置过滤（`canFilterFriend` 为 true）
- **功能**: 查看和管理被过滤的不符合要求的牛人
- **摘要**: 
  - 有过滤时: "近30天过滤X位不符合职位要求的牛人"
  - 无过滤时: "点击可设置过滤要求"

### 4. 不合适的牛人 (BossUnfit) - Boss专属
- **ID**: `CHAT_UNFIT` = Number.MAX_SAFE_INTEGER - 4
- **适用身份**: 仅 Boss
- **图标**: `app.media.ic_f2_unfit`
- **显示条件**: 有不合适的联系人（`getUnfitContact().length > 0`）
- **功能**: 管理标记为不合适的牛人
- **摘要**: 显示联系人数量，如 "X个联系人"
- **时间**: 固定为 0

### 5. 已过滤消息 (VIPAndHighGeek) - Geek专属
- **ID**: `CHAT_811_VIP_HIGH_GEEK` = Number.MAX_SAFE_INTEGER - 20
- **适用身份**: 仅 Geek
- **图标**: `app.media.icon_vip_choose`
- **显示条件**: 复杂条件组合
  - 命中灰度策略 (isGray)
  - 是VIP用户 (canFilterFriend)
  - 有VIP过滤的消息 (vipFilterCount > 0)
  - 开启了防打扰 (isOpenSilent)
  - 有高端技术牛人消息 (highTechCount > 0)
- **功能**: 查看被过滤的Boss消息
- **过滤类型**:
  1. VIP过滤：`isFiltered = 1` 的联系人
  2. 高端技术牛人防打扰：
     - 新类型：`highGeek = 1` 的联系人
     - 旧类型：`haveSentMsg = 0` 的联系人（未发送过消息）
- **摘要**: 根据不同情况显示
  - 有过滤: "近X天已经过滤Y位Boss的消息"
  - VIP无过滤: "点击可设置过滤要求"
  - 非VIP灰度: "点击可开启自动过滤"
  - 已开启防打扰无过滤: "已开启自动过滤"
- **未读数**: 累计所有被过滤且有未读消息的联系人的未读总数

### 6. 不感兴趣的BOSS (GeekUnInterest) - Geek专属
- **ID**: `CHAT_UNFIT` = Number.MAX_SAFE_INTEGER - 4（与BossUnfit共用）
- **适用身份**: 仅 Geek
- **图标**: `app.media.ic_f2_unfit`
- **显示条件**: 有不感兴趣的联系人（`getUninterestedContact().length > 0`）
- **功能**: 管理标记为不感兴趣的Boss
- **摘要**: 显示联系人数量，如 "X个联系人"
- **时间**: 固定为 0

## 判断逻辑

### 1. 是否为虚拟联系人
```typescript
F2ContactModeManager.isLocalContact(friendId)
```
该方法检查 friendId 是否在 `LocalModeImp` 注册的虚拟联系人 ID 列表中。

### 2. 显示顺序
在 `AllContactPagingSource` 中的排序规则：
- 首先按聊天时间降序
- 时间相同时，虚拟联系人排在真实联系人后面
- 同类型时，按 ID 降序

### 3. 时间分配策略
在 `AllContactPagingSource.getVirtualContactTime()` 中：
- 批量追聊、不合适类：1天前
- 职位推荐、VIP高级牛人：3天前
- 默认：2天前

## 技术实现要点

1. **注册机制**: 所有虚拟联系人在 `LocalModeImp.registerLocalMode()` 中注册
2. **条件判断**: 每个虚拟联系人的 `getLocal()` 方法负责判断是否应该显示
3. **清除未读**: 每个虚拟联系人实现 `cleanUnread()` 方法处理未读消息清除
4. **身份隔离**: Boss 和 Geek 看到不同的虚拟联系人集合

## 与 Paging3 的集成

`AllContactPagingSource` 通过以下方式集成虚拟联系人：
1. 获取虚拟联系人列表（带缓存）
2. 根据加载方向和时间范围过滤相关虚拟联系人
3. 与真实联系人混合并排序
4. 在总数统计中包含虚拟联系人数量 