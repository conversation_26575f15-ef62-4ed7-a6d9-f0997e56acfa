# 虚拟联系人事件驱动更新机制

## 概述

`VirtualContactManager` 现在使用事件驱动机制来保持虚拟联系人数据的实时性，替代了之前的5分钟缓存策略。这确保了虚拟联系人数据始终与底层数据保持同步。

## 事件监听机制

### 监听的事件

1. **ZPEvents.Contact.SyncAll**
   - 触发时机：联系人数据同步
   - 影响：可能影响批量追聊、不合适联系人等的数量和状态

2. **ZPEvents.Contact.LoadContact**
   - 触发时机：从服务器加载新的联系人数据（如 F2TipBarManager 更新）
   - 影响：职位推荐设置、过滤设置等配置变化

3. **ZPEvents.Contact.UnReaCount**
   - 触发时机：未读数量变化
   - 影响：所有虚拟联系人的未读数更新

4. **ZPEvents.Contact.BatchSync**
   - 触发时机：批量同步联系人
   - 影响：批量数据变化

## 使用指南

### 1. 初始化（在应用启动时）

```typescript
import { initVirtualContactMonitoring } from 'lib_kernel';

// 在应用入口处初始化
export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 初始化虚拟联系人监听
    initVirtualContactMonitoring(this.context.eventHub);
  }
}
```

### 2. 自动更新流程

```
数据变化 → 触发事件 → VirtualContactManager 接收事件
    ↓
清除缓存 → 重新计算虚拟联系人
    ↓
通知所有注册的回调 → AllContactPagingSource 刷新数据
```

### 3. 手动触发更新（如需要）

```typescript
// 强制刷新虚拟联系人数据
await VirtualContactManager.getInstance().clearCacheAndNotify();
```

### 4. 注册自定义回调

```typescript
// 注册数据变更回调
const callback = () => {
  console.log('Virtual contacts updated!');
  // 执行自定义逻辑
};

VirtualContactManager.getInstance().registerChangeCallback(callback);

// 不再需要时移除回调
VirtualContactManager.getInstance().unregisterChangeCallback(callback);
```

## 数据流示例

### 场景1：Boss设置了职位过滤

1. 用户在设置页面配置过滤条件
2. 服务器返回新的配置数据
3. `ContactBannerManager.getF2ContactTipBar()` 更新 `F2TipBarManager`
4. 发送 `ZPEvents.Contact.LoadContact` 事件
5. `VirtualContactManager` 接收事件并刷新数据
6. "不符合要求牛人" 虚拟联系人更新显示

### 场景2：新消息到达

1. MQTT 收到新消息
2. 联系人数据更新
3. 发送 `ZPEvents.Contact.SyncAll` 事件
4. `VirtualContactManager` 接收事件并刷新数据
5. 相关虚拟联系人的未读数更新

## 性能考虑

1. **防抖处理**：多个事件快速触发时，内部会进行适当的防抖处理
2. **内存缓存**：计算结果缓存在内存中，相同请求直接返回
3. **异步处理**：所有数据库查询都是异步的，不阻塞主线程

## 调试

启用调试日志查看事件流：

```typescript
// 查看 VirtualContactManager 的日志
Logger.setLevel(TAG, LogLevel.DEBUG);
```

日志示例：
```
[VirtualContactManager] Load contact event received
[VirtualContactManager] Retrieved 3 virtual contacts
[AllContactPagingSource] Virtual contacts changed, invalidating data source
```

## 注意事项

1. 必须在应用启动时初始化监听，否则无法接收事件
2. 在应用退出时应调用 `cleanupVirtualContactMonitoring()` 清理资源
3. 回调函数应避免执行耗时操作，以免影响性能
4. 事件是全局的，所有 `VirtualContactManager` 的使用者都会收到通知 