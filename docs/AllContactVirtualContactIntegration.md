# AllContactPagingSource 虚拟联系人集成方案

## 概述

为了保证 `AllContactPagingSource` 与 `F2ContactManager` 的功能同步，我们在 `AllContactPagingSource` 中直接集成了虚拟联系人（功能抽屉）的支持。这个方案采用了最小化改动的原则，只在 `AllContactPagingSource` 内部进行修改。

## 实现要点

### 1. 虚拟联系人获取

- 通过 `F2ContactModeManager.getAllContact()` 获取所有联系人
- 使用 `F2ContactModeManager.isLocalContact()` 过滤出虚拟联系人
- 实现了 5 分钟缓存机制，避免频繁调用

### 2. 时间戳分配

为虚拟联系人分配合理的时间戳，根据不同类型：
- 批量追聊、不合适等：1 天前
- 职位推荐设置、VIP 高级牛人：3 天前
- 默认：2 天前

### 3. 数据混合策略

根据不同的加载方向采用不同的混合策略：

#### INIT/REFRESH（初始加载/刷新）
- 获取时间范围内的所有虚拟联系人
- 与真实联系人合并后按时间排序

#### APPEND（向后加载）
- 获取比当前页最早时间更早的虚拟联系人
- 限制在 7 天内，避免过多历史数据

#### PREPEND（向前加载）
- 获取比当前页最晚时间更晚的虚拟联系人
- 不超过当前时间

### 4. 排序规则

- 首先按聊天时间降序排列
- 时间相同时，虚拟联系人排在真实联系人后面
- 同类型时，按 ID 降序排列

### 5. 特殊处理

#### loadMixedData 方法
- 使用统一的 `mixVirtualContactsForDirection` 方法处理虚拟联系人
- 与其他加载方式（APPEND/PREPEND）保持一致的范围判断逻辑
- 确保只插入在当前时间范围内的虚拟联系人

#### getAllCount 方法
- 返回真实联系人数量 + 虚拟联系人数量
- 确保分页状态计算正确

## 优势

1. **最小化改动**：所有修改都限制在 `AllContactPagingSource` 内部
2. **功能同步**：与 `F2ContactManager` 的"全部"分组保持一致
3. **性能优化**：使用缓存机制，避免频繁查询
4. **灵活性**：可以根据业务需求调整虚拟联系人的时间和排序

## 注意事项

1. 虚拟联系人的时间戳需要根据业务逻辑合理设置
2. 缓存时间（5 分钟）可以根据实际需求调整
3. APPEND 操作限制在 7 天内，防止加载过多历史虚拟联系人 