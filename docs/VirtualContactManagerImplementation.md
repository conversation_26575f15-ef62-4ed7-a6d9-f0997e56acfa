# VirtualContactManager 实现文档

## 概述

`VirtualContactManager` 是一个独立的单例管理器，用于统一管理所有虚拟联系人（功能抽屉）的创建和显示逻辑。它通过 SQL 查询替代了对 `F2ContactManager` 的依赖，实现了更好的解耦和职责分离。

## 主要特性

### 1. 单例模式
```typescript
public static getInstance(): VirtualContactManager
```
确保整个应用只有一个实例，统一管理虚拟联系人。

### 2. 事件驱动更新
- 监听联系人相关事件，实时更新虚拟联系人数据
- 监听的事件包括：
  - `ZPEvents.Contact.SyncAll` - 联系人同步
  - `ZPEvents.Contact.LoadContact` - 加载联系人（服务器数据更新）
  - `ZPEvents.Contact.UnReaCount` - 未读数变化
  - `ZPEvents.Contact.BatchSync` - 批量同步
- 使用内存缓存提升查询性能

### 3. SQL 查询替代
通过直接的 SQL 查询获取所需数据，而不依赖 `F2ContactManager`：

```typescript
// 批量追聊查询
SELECT * FROM Contact 
WHERE status > -1 
  AND friendId > 1000 
  AND isBatchChatBack = 1 
  AND isRejectUser = 0
ORDER BY local_chatTime DESC

// 不合适联系人查询
SELECT COUNT(*) as count FROM Contact 
WHERE status > -1 
  AND friendId > 1000 
  AND contactState = ${F2FriendType.UnfitContact}
```

## API 接口

### 获取虚拟联系人
```typescript
async getVirtualContacts(): Promise<ContactEntity[]>
```
根据用户身份（Boss/Geek）返回相应的虚拟联系人列表。

### 判断虚拟联系人
```typescript
isVirtualContact(friendId: number): boolean
```
快速判断给定的 friendId 是否为虚拟联系人。

### 初始化监听
```typescript
initVirtualContactMonitoring(eventHub: common.EventHub): void
```
在应用启动时调用，开始监听虚拟联系人相关事件。

### 清理监听
```typescript
cleanupVirtualContactMonitoring(): void
```
在应用关闭时调用，停止事件监听。

### 注册变更回调
```typescript
registerChangeCallback(callback: () => void): void
```
注册虚拟联系人数据变更回调，当数据发生变化时会被调用。

### 清除缓存并通知
```typescript
clearCacheAndNotify(): Promise<void>
```
清除缓存并触发所有注册的回调。

## 实现细节

### Boss 身份虚拟联系人
1. **职位推荐设置** - 基于 `BossPositionRequestSettingManager`
2. **批量追聊** - 通过 SQL 查询批量追聊联系人
3. **不符合要求牛人** - 查询被 VIP 过滤的联系人
4. **不合适的牛人** - 查询标记为不合适的联系人

### Geek 身份虚拟联系人
1. **职位推荐设置** - 基于 `GeekPositionRequestSettingManager`
2. **已过滤消息** - 完整实现，包括：
   - VIP过滤的未读消息（`isFiltered = 1`）
   - 高端技术牛人防打扰（新类型：`highGeek = 1`，旧类型：`haveSentMsg = 0`）
   - 根据不同条件显示不同摘要文案
   - 累计所有过滤消息的未读数
3. **不感兴趣的BOSS** - 查询标记为不感兴趣的联系人

## 与 AllContactPagingSource 的集成

`AllContactPagingSource` 已更新为使用 `VirtualContactManager`：

```typescript
// 在应用启动时初始化监听
initVirtualContactMonitoring(context.eventHub);

// 获取虚拟联系人
const virtualContacts = await VirtualContactManager.getInstance().getVirtualContacts();

// 判断是否为虚拟联系人
const isVirtual = VirtualContactManager.getInstance().isVirtualContact(friendId);

// AllContactPagingSource 自动注册了变更回调
// 当虚拟联系人数据变化时会自动触发数据源失效
```

## 优势

1. **解耦** - 减少对 `F2ContactManager` 的依赖
2. **实时性** - 事件驱动架构，数据变更实时反映
3. **性能** - 内存缓存和直接 SQL 查询提升性能
4. **可维护性** - 集中管理虚拟联系人逻辑
5. **可测试性** - 独立的单例更容易进行单元测试
6. **扩展性** - 易于添加新的虚拟联系人类型和事件监听
7. **一致性** - 自动同步更新，避免数据不一致

## 注意事项

1. 必须在应用启动时调用 `initVirtualContactMonitoring` 初始化事件监听
2. 异步方法需要使用 `await` 调用
3. 高端技术牛人有新旧两种判断逻辑，需要根据 `isNewType()` 动态选择
4. 数据变更是事件驱动的，无需手动刷新
5. 注册的回调会在每次数据变更时被调用，注意性能影响 