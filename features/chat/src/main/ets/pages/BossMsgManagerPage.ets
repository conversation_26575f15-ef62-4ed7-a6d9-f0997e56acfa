import { AnalyticsFactory, GroupType } from 'lib_kernel/Index';
import { CommonSearch, FontColors, ObservedObj } from 'lib_zpui/Index';
import { NavigationUtils, QRScanUtils, StringUtil, TextUtils, ToastUtils, ZPConstants } from 'lib_zputils/Index';
import { NoticeRouter, NoticeSource } from 'lib_zpexport/Index';
import { ContactClassifyItem } from '../bean/ContactClassifyItem';
import ChatStyles from '../ChatStyles';
import { BossInteractComponent } from '../components/interact/boss/BossInteractComponent';
import { NonReadCountComponent } from '../components/NonReadCountComponent';
import { BossContactSettingDialog } from '../dialog/BossContactSettingDialog';
import { BossMsgManagerModel } from '../model/BossMsgManagerModel';
import { ContactsListComponent } from './ContactsListComponent';
import { ContactListNewComponent } from './ContactListNewComponent';
import { ChatTitleComponent } from '../components/ChatTitleComponent';
import { ContactBannerView } from '../components/ContactBannerView';


const CHAT_TAB_INDEX = 0
const INTERACT_TAB_INDEX = 1

@Component
export struct BossMsgManagerPage {
  @State model: BossMsgManagerModel = new BossMsgManagerModel()

  @State @Watch('setRefreshing') modelRefreshing: ObservedObj<boolean> = this.model.isRefreshing
  @Provide freshTimes: ObservedObj<number> = this.model.freshTimes
  @Provide isGrayOnlyContactToAll: ObservedObj<boolean> = this.model.isGrayOnlyContactToAll
  @State isRefreshing: boolean = false

  private mainTabTitles: string[] = ['聊天', '互动']
  private mainTabScroller: Scroller = new Scroller()
  @Link mainTabIndex: number
  @Link subTabIndex: number
  @Link forceRefresh: boolean

  private panOptionLeft: PanGestureOptions = new PanGestureOptions({ direction: PanDirection.Left });

  private controller: TabsController = new TabsController();

  /**
   * 通知
   */
  @State notifyNonReadCount: ObservedObj<number> = this.model.notifyNonReadCount
  @State isNotifyNoneReadSilent: ObservedObj<boolean> = this.model.isNotifyNoneReadSilent
  @State isNotifyNonReadDotGrey: ObservedObj<boolean> = this.model.isNotifyNonReadDotGrey
  @Consume @Watch('onPageShowStatusChanged') isPageShowed: ObservedObj<boolean>
  setRefreshing() {
    this.isRefreshing = this.modelRefreshing.obj ?? false
  }

  aboutToAppear(): void {
    this.model.initData()
    this.model.initEmit()
    this.model.requestWindowConfig();
    this.model.getF2ContactTipBar();
  }

  onPageShowStatusChanged() {
    if(this.isPageShowed.obj) {
      if(TextUtils.isNotEmpty(this.model.pageShowToast)){
        ToastUtils.showToast(this.model.pageShowToast)
        this.model.pageShowToast = ''
      }
      this.model.requestWindowConfig();
      this.model.getF2ContactTipBar();
      this.model.loadVipStatus()
    }
  }
  aboutToDisappear(): void {
    this.model.aboutToDisappear()
  }

  build() {
    Column() {
      Stack() {
        Image($r('app.media.ic_boss_title_style_bg'))
          .height(60)
          .objectFit(ImageFit.Fill)
          .width(ZPConstants.FULL_PERCENT)
        this.tabsBuilder()
      }.width(ZPConstants.FULL_PERCENT)
      .alignContent(Alignment.Top)

      Tabs({
        index: this.mainTabIndex,
        barPosition: BarPosition.Start,
        controller: this.controller
      }) {
        ForEach(this.mainTabTitles, (title: string, index: number) => {
          TabContent() {
            if (index == CHAT_TAB_INDEX) {
              Refresh({ refreshing: $$this.isRefreshing, offset: 120, friction: 100 }) {
                this.mainBody()
              }.onRefreshing(() => {
                this.model.syncRead()
              })
            } else {
              BossInteractComponent({
                forceRefresh: this.forceRefresh,
                currentIndex: this.subTabIndex,
                onSwipeRight: () => this.controller.changeIndex(CHAT_TAB_INDEX)
              })
            }
          }
        })
      }.layoutWeight(1)
      .barHeight(0)
      .onChange((index) => {
        this.mainTabIndex = index
      })
    }
    .width(ZPConstants.FULL_PERCENT)
  }

  @Builder
  buildBanner(){
    if(this.model.windowBean){
      ContactBannerView({
        bean: this.model.windowBean,
        onCloseClick: () => {
          this.model.bannerCloseClick()
        },
        onCardClick: () =>{
          this.model.bannerCardClick()
        }
      })
    }
  }

  @Builder
  mainBody() {
    Column() {
      this.buildBanner()
      if (this.model.showSearch) {
        CommonSearch({
          hint: '搜索联系人、公司、聊天记录、备注',
          searchBackgroundColor: $r('app.color.FFF5F5F5_FF262629'),
          searchBorderWidth: 0,
          verticalPadding: 6,
          enable: false,
        }).margin({ top: 11, left: 20, right: 20 })
          .height(34)
          .onClick(()=>{
            NavigationUtils.pushPage({
              pageName: 'ContactSearchPage',
            })
          })
      }

      this.classifyBuilder()

      Tabs({
        index: this.model.tabIndex,
        barPosition: BarPosition.Start
      }) {
        ForEach(this.model.groupTypes, (item: number, index: number) => {
          if(index == this.model.groupTypes.length - 1){
            TabContent() {
              ContactListNewComponent({ groupType: item })
            }.gesture(
              PanGesture(this.panOptionLeft)
                .onActionEnd(() => {
                  this.controller.changeIndex(INTERACT_TAB_INDEX)
                })
            )
          } else {
            TabContent() {
              ContactListNewComponent({ groupType: item })
            }
          }
        })
      }
      .backgroundColor("#F5F5F6")
      .barHeight(0)
      .vertical(false)
      .scrollable(true)
      .barMode(BarMode.Scrollable)
      .layoutWeight(1)
      .onChange((index) => {
        this.model.tabIndex = index
        this.model.tabClassifyClick()
        this.model.initContactGroupData()
      })
    }.height(ZPConstants.FULL_PERCENT)
  }

  //IM设置弹窗
  private showBossMsgSettingDialog() {
    const controller = new CustomDialogController({
      builder: BossContactSettingDialog({
        onCloseClick: () => {
          controller.close()
        }
      }),
      alignment: DialogAlignment.Bottom,
      customStyle: true,
    })
    controller.open()
  }

  @Builder
  classifyBuilder() {
    Row() {
      if (this.model.classifyItems.length > 1) {
        List({ space: 18 }) {
          ForEach(this.model.classifyItems, (item: ContactClassifyItem, index: number) => {
            ListItem() {
              Flex({
                alignItems: ItemAlign.Stretch,
                direction: FlexDirection.Column
              }) {
                Blank().height(16)
                Row() {
                  Text(StringUtil.getMaxCharText(item.title, 5, false, false))
                    .fontColor(this.model.tabIndex === index ? FontColors.text_FF292929_FFD2D2D6 :
                    FontColors.text_FF5E5E5E_FF9E9EA1)
                    .fontSize(14)
                    .fontWeight(this.model.tabIndex === index ? 500 : 400)
                  if (index == 0) {
                    Image($r('app.media.ic_f2_arrow_down'))
                      .width(8)
                      .height(4)
                  }

                  if (index > 0 && item.noneReadCount > 0) {
                    // Row()
                    //   .width(4)
                    //   .height(4)
                    //   .backgroundColor(Color.Red)
                    //   .borderRadius(4)
                    //   .margin({ bottom: 14, left: 2 })
                    Text((item.noneReadCount > 99 ? '99+' : item.noneReadCount.toString()))
                      .fontColor(FontColors.text_FF5E5E5E_FF9E9EA1)
                      .fontSize(10)
                      .backgroundColor($r('app.color.FFF0F0F0_FF303033'))
                      .borderRadius(10)
                      .padding(3)
                      .height(16)
                      .textAlign(TextAlign.Center)
                      .maxLines(1)
                      .margin({ left: 2 })
                      .constraintSize({ minWidth: 16 })
                  }
                }

                Line()
                  .strokeWidth(6)
                  .borderRadius(2)
                  .linearGradient({
                    angle: 90,
                    colors: [[0xFF15B3B3, 0.0], [0x00FFFFFF, 1.0]]
                  })
                  .opacity(this.model.tabIndex === index ? 1 : 0)
                  .translate({ y: -3 })
              }
            }.onClick(() => {
              if (index == 0) {
                if (this.model.tabIndex == index) {
                  this.model.toChatPositionFilter()
                }
              }
              this.model.tabIndex = index
              if (item.groupType > 0) {
                AnalyticsFactory.create()
                  .action("f2-msg-filter-click")
                  .param("p", item.groupType)
                  .param("p2", item.noneReadCount)
                  .build();
              }

              //todo

            })

            // .bindPopup(this.getShowContactTabGuideVisible(item), {
            //   builder: this.contactTabGuide(),
            //   placement: Placement.Bottom,
            //   enableArrow: true,
            //   popupColor: $r('app.color.FF15B3B3'),
            //   backgroundBlurStyle: BlurStyle.NONE,
            //   mask: false,
            //   autoCancel: false,
            //   onStateChange: (e) => {
            //     if (!e.isVisible) {
            //       this.model.showContactTabGuide = false
            //     }
            //   }
            //
            // })
          })
        }
        .contentStartOffset(20)
        .listDirection(Axis.Horizontal)
        .scrollBar(BarState.Off)
        .edgeEffect(EdgeEffect.None)
        .width(ZPConstants.FULL_PERCENT)
        .height(40)
        .layoutWeight(1)
      }

      Blank()

      this.filterBuilder()

    }.width(ZPConstants.FULL_PERCENT)
    .padding({right: 20})
    .alignItems(VerticalAlign.Top)
  }

  @Builder
  filterBuilder(){
    if (this.model.f2ContactManager.isFilterOptionOn()) {
      Text() {
        ImageSpan(this.model.f2ContactManager.hasFilter()  ? $r('app.media.geek_icon_filter_area_right') : $r('app.media.chat_icon_contact_filter'))
          .height(12)
          .verticalAlign(ImageSpanAlignment.CENTER)
          .margin({ right: 8 })

        Span(this.model.filterText)
          .fontColor(this.model.f2ContactManager.hasFilter() ? $r('app.color.FF0D9EA3') : $r('app.color.FF5E5E5E_FF9E9EA1'))
          .fontSize(12)
      }.margin({ left: 10, top: 13 })
      .padding({ left: 6, top: 3, right: 6, bottom: 2 })
      .borderWidth(0.7)
      .borderColor(this.model.f2ContactManager.hasFilter() ? $r('app.color.FF15B3B3') : $r('app.color.FFB8B8B8_FF6D6D70'))
      .borderRadius(4)
      .onClick(() => {
        this.model.filterClick()
      }).bindPopup(this.model.showFilterPopup, {
        builder: this.popupFilterBuilder(),
        arrowHeight: 7, // 设置气泡箭头高度
        arrowWidth: 10, // 设置气泡箭头宽度
        radius: 8, // 设置气泡的圆角
        backgroundBlurStyle: BlurStyle.NONE,
        popupColor: Color.White,
        shadow: { radius: 10, color: $r('app.color.1F000000') },
        onStateChange: (event) => {
          if (!event.isVisible) {
            this.model.showFilterPopup = false
          }
        }
      })
    }
  }

  @Builder
  popupFilterBuilder(){
    Column(){
      Row(){
        Image($r('app.media.ic_greeting_o'))
          .width(20)
          .height(20)
        Blank().width(8)
        Text('只看未读')
          .fontSize(16)
          .fontColor(FontColors.text_FF141414_FFE6E6EB)
          .fontWeight(FontWeight.Bold)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }.padding({
        left: 20,
        right: 20,
        top: 10,
        bottom: 10
      }).onClick(() => {
        this.model.selectUnreadFilter()
      })
      if (this.model.f2ContactManager.isSupportItemSourceFilterOn()) {
        Row(){
          Image($r('app.media.ic_contact_source_filter'))
            .width(20)
            .height(20)
          Blank().width(8)
          Text('道具筛选')
            .fontSize(16)
            .fontColor(FontColors.text_FF141414_FFE6E6EB)
            .fontWeight(FontWeight.Bold)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
        }.padding({
          left: 20,
          right: 20,
          top: 10,
          bottom: 10
        }).onClick(() => {
          this.model.selectSourceFilter()
        })
      }

      if (this.model.isBossVipFriendFilter) {
        Stack() {
          Row(){
            Image($r('app.media.ic_contact_filter_setting'))
              .width(20)
              .height(20)
            Blank().width(8)
            Text('过滤设置')
              .fontSize(16)
              .fontColor(FontColors.text_FF141414_FFE6E6EB)
              .fontWeight(FontWeight.Bold)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
          }.padding({
            left: 20,
            right: 20,
            top: 10,
            bottom: 10
          })

          Image($r('app.media.ic_vip_filter'))
            .width(20)
            .height(11).margin({right: 10})

        }.alignContent(Alignment.TopEnd)
        .onClick(() => {
          this.model.startSkipBlockVip()
        })
      }

      //12.04先不做
      // Row(){
      //   Image($r('app.media.ic_contact_query_filter'))
      //     .width(20)
      //     .height(20)
      //   Blank().width(8)
      //   Text('极速筛选')
      //     .fontSize(16)
      //     .fontColor(FontColors.text_FF141414_FFE6E6EB)
      //     .fontWeight(FontWeight.Bold)
      //     .maxLines(1)
      //     .textOverflow({ overflow: TextOverflow.Ellipsis })
      // }.padding({
      //   left: 20,
      //   right: 20,
      //   top: 10,
      //   bottom: 10
      // })
    }
  }

  getShowContactTabGuideVisible(item: ContactClassifyItem){
    return this.model.showContactTabGuide && this.model.showContactTabGuideGroupType == item.groupType && item.groupType != GroupType.ALL
  }

  @Builder
  contactTabGuide() {
    Row() {
      Image(this.model.showContactTabGuideIcon)
        .width(28)
        .height(28)
        .margin({ left: 12 })
        .clip(new Circle().width(28).height(28))
      Text(this.model.getContactTabTip())
        .fontSize(14)
        .fontColor(FontColors.text_white)
        .padding({ top: 8, bottom: 8 })
        .margin({ left: 8 })

    }.padding({ right: 10 })
    .borderRadius(12)
    .onAppear(() => {
      this.model.setShowContactTabGuide()
    })
  }

  @Builder
  tabsBuilder() {
    Row() {
      List({ scroller: this.mainTabScroller, space: 24 }) {
        ForEach(this.mainTabTitles, (title: string, index: number) => {
          ListItem() {
            if (index == CHAT_TAB_INDEX) {
              ChatTitleComponent({
                isSyncing: this.model.isSyncing,
                title: title,
                selected: this.mainTabIndex == index,
                noneReadMsgCount: this.model.noneReadMsgCount
              })
            } else {
              this.InteractBuilder(title, index)
            }
          }
          .onClick(() => {this.mainTabIndex = index})
        })
      }.listDirection(Axis.Horizontal)
      .alignListItem(ListItemAlign.Center)

      Blank()

      if (false) {
        Image($r('app.media.ic_style_ui_action_scan_new'))
          .attributeModifier(ChatStyles.tabImageModifier)
          .onClick(() => {
            QRScanUtils.openScan(getContext(this), "4", '', '')
          })
      }

      Image($r('app.media.ic_style_ui_action_settings_new'))
        .attributeModifier(ChatStyles.tabImageModifier)
        .onClick(() => {
          this.showBossMsgSettingDialog()
        })

      if (false) {
        Stack({ alignContent: Alignment.TopEnd }) {
          Image($r('app.media.ic_style_ui_action_notify'))
            .attributeModifier(ChatStyles.tabImageModifier)
            .onClick(() => {
              NoticeRouter.startNotice(NoticeSource.SOURCE_F2_TOP)
            })

          // 消息未读数
          NonReadCountComponent({
            nonReadCount: this.notifyNonReadCount.obj,
            grey: this.isNotifyNonReadDotGrey.obj,
            silent: this.isNotifyNoneReadSilent.obj
          }).padding({ top: 5, right: 5 })
        }
      }
    }
    .alignItems(VerticalAlign.Center)
    .width(ZPConstants.FULL_PERCENT)
    .height('44vp')
    .backgroundColor(Color.Transparent)
    .padding({ left: 20, right: 10 })
    .margin({ top: AppStorage.get<number>('statusBarHeight') })
  }

  @Builder
  InteractBuilder(title: string, index: number) {
    Row() {
      Text(title)
        .tabTextStyle(this.mainTabIndex == index)

      if (this.model.hasNoneReadInteract) {
        Row()
          .width(5)
          .height(5)
          .borderRadius(5)
          .backgroundColor(Color.Red)
          .margin({ top: 2 })
      }
    }.alignItems(VerticalAlign.Top)
  }
}

@Extend(Text)
function tabTextStyle(selected: boolean) {
  .fontSize(selected ? 24 : 18)
  .fontWeight(selected ? FontWeight.Bold : FontWeight.Normal)
  .fontColor(selected ? $r('app.color.FF141414_FFE6E6EB') : $r('app.color.FF858585'))
}