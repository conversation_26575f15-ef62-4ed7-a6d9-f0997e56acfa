import { ChatUrlConfig, HttpResponse, POST, ZPHttpError } from 'lib_http/Index';
import {
  CommonConfigManager,
  ContactEntity,
  ContactGroupBean,
  ContactMessageTimeRangeRepository,
  F2ContactFilterType,
  F2FriendType as FriendType,
  GroupType,
  Kernel
} from 'lib_kernel';
import { BasicDataSource, FontColors, ObservedObj } from 'lib_zpui';
import { CheckUtils, DateUtils, TextUtils, ToastUtils, ZPConstants, ZPEvents, ZPStore } from 'lib_zputils';
import { SyncGeekInfoResponse } from '../api/SyncGeekInfoResponse';
import { GroupContactComponent } from '../components/contact_item/GroupContactComponent';
import { NewHelloSortComponent } from '../components/contact_item/NewHelloSortComponent';
import { NonFoldTopDividerContactComponent } from '../components/contact_item/NonFoldTopDividerContactComponent';
import { SystemContactComponent } from '../components/contact_item/SystemContactComponent';
import { TopDividerContactComponent } from '../components/contact_item/TopDividerContactComponent';
import { WeekAgeContactComponent } from '../components/contact_item/WeekAgeContactComponent';
import { SingleContactComponent } from '../components/SingleContactComponent';


/**
 * 联系人列表
 */
@Component
export struct ContactsListComponent {
  @State contactList: BasicDataSource<ContactEntity> = new BasicDataSource<ContactEntity>()
  @State footerText: string = ''
  selectedGroupType?: number
  @Consume @Watch('initContactList') freshTimes:  ObservedObj<number>
  @Consume isGrayOnlyContactToAll:  ObservedObj<boolean>
  groupType: number = GroupType.ALL
  @State isNotEmpty: boolean = false
  @State sortType: number = ContactGroupBean.RECOMMEND_SORT_TYPE
  private f2ContactManager = Kernel.getInstance().getContactService().getF2ContactManager()

  @State showGuideUnread: boolean = false

  @State guideUnreadTip: string = ''

  async aboutToAppear() {
    this.initContactList()
    this.footerText = await ContactMessageTimeRangeRepository.getInstance().getContactTimeRangeText()
    if (Kernel.getInstance().isBossRole()) {
      this.syncGeekInfo()
    }
    this.initSortType()
  }
  initSortType(){
    if (Kernel.getInstance().isBossRole()) {
      this.sortType = this.f2ContactManager.isNewContactRecommendSort(GroupType.NEW_GREETING) ? ContactGroupBean.RECOMMEND_SORT_TYPE : ContactGroupBean.TIME_SORT_TYPE
    } else {
      this.sortType = ContactGroupBean.NONE_SORT_TYPE
    }
  }
  async initShowGuileUnread() {
    if (this.groupType == GroupType.ALL) {
      const selectGroup = this.f2ContactManager.getContactGroupData(this.selectedGroupType ?? 0)
      if (this.isGrayOnlyContactToAll.obj == true) {
        this.isGrayOnlyContactToAll.obj = false
        if (this.f2ContactManager.isFilterOptionOn()) {
          if (this.f2ContactManager.getSelectFilter() != 1) {
            const f2ContactUnreadLimit = CommonConfigManager.getInstance().isF2ContactUnreadLimit();
            if (f2ContactUnreadLimit < 0 || selectGroup.noneReadContactCount < f2ContactUnreadLimit) {
              return;
            }
            const newContactGuideUnreadTime = await ZPStore.getValueUid<number>("new_contact_guide_unread_time") ?? 0;
            if (DateUtils.isToday(newContactGuideUnreadTime)) { //同一天不走
              return;
            }

            if (this.f2ContactManager.getSelectFilter() == 1) { //已经勾选了未读
              return;
            }

            if (!this.showGuideUnread) {
              this.showGuideUnread = true
              if (selectGroup.noneReadCount > 0) {
                this.guideUnreadTip = `有超过${selectGroup.noneReadCount}条未读消息，点击可快速查看`;
              } else {
                this.guideUnreadTip = "有较多未读消息，点击可快速查看";
              }
              setTimeout(() => {
                this.showGuideUnread = false
              }, 5000)
            }
          }
        }
      }
      if (this.f2ContactManager.getSelectFilter() == 1 ||
        (selectGroup.redDot <= 0 && selectGroup.noneReadCount <= 0)) { //1、选择未读隐藏提示 2、处理完所有未读的消息
        this.showGuideUnread = false
      }
    }
  }

  //获取联系人列表
  private initContactList() {
    const group = this.f2ContactManager.getContactGroupData(this.groupType)
    let newContacts = new Array<ContactEntity>()
    if (group) {
      //第一：新招呼排序Tab
      this.f2ContactManager.setContactHelloDataSource(group, newContacts)
      //第二：置顶数据
      let topContacts = this.f2ContactManager.setContactTopDataSource(group, newContacts)
      //第三：添加一周标记分割线
      this.f2ContactManager.setContactWeekAgeDataSource(group.weekAgeContactDivider, topContacts);
      //第四：添加剩余数据
      newContacts = newContacts.concat(topContacts)
    }
    this.contactList.setUpdateData(newContacts)

    this.isNotEmpty = this.contactList.totalCount() > 0
    this.initShowGuileUnread()
    this.initSortType()
    if (Kernel.getInstance().isBossRole()) {
      this.syncGeekInfo()
    }
  }

  build() {
    Column() {
      if (this.isNotEmpty) {
        List() {
          ListItem() {
            if(this.showGuideUnread){
              Row() {
                Image($r('app.media.ic_resume_close_green'))
                  .width(10)
                  .height(10)
                  .onClick(()=>{
                    this.showGuideUnread = false
                    ZPStore.setValueUid("new_contact_guide_unread_time", new Date().getTime());
                  })
                Text(this.guideUnreadTip)
                  .fontSize(13)
                  .fontColor($r('app.color.FF0D9EA3'))
                  .layoutWeight(1)
                  .margin({left: 8, right: 12})
                  .padding({top: 8, bottom: 8})
                Text('查看未读')
                  .fontSize(13)
                  .fontColor(FontColors.text_white)
                  .backgroundColor($r('app.color.FF15B3B3'))
                  .padding({
                    left: 10,
                    right: 10,
                    top: 3,
                    bottom: 3
                  })
                  .textAlign(TextAlign.Center)
                  .borderRadius(11)
                  .onClick(()=>{
                    this.showGuideUnread = false
                    const bossFilterData = this.f2ContactManager.getBossFilterData();
                    if (bossFilterData) { //自动选择未读
                      bossFilterData.selectUnreadOption();
                      this.setFilterParam(F2ContactFilterType.BossMulti);
                    } else {
                      this.setFilterParam(F2ContactFilterType.Unread);
                    }
                  })
              }
              .backgroundColor($r('app.color.2915B3B3'))
              .padding({left: 20, right: 20})
              .width(ZPConstants.FULL_PERCENT)
            }

          }
          LazyForEach(this.contactList,
            (contact: ContactEntity, index) => {
              ListItem() {
                this.ContactListItem(contact, index)
              }
            }, (item: ContactEntity, index) => {
              return JSON.stringify(item) + index
            })

          ListItem() {
            Text(this.footerText)
              .padding(15)
              .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
              .fontSize(14)
              .backgroundColor($r('app.color.FFF5F5F5_FF262629'))
              .textAlign(TextAlign.Center)
              .width(ZPConstants.FULL_PERCENT)
          }
        }.layoutWeight(1)
        .scrollBar(BarState.Off)
        .edgeEffect(EdgeEffect.None)
      } else {
        this.emptyViewBuilder()
      }
    }.width(ZPConstants.FULL_PERCENT)
    .height(ZPConstants.FULL_PERCENT)
  }

  public setFilterParam(filter: F2ContactFilterType) {
    const selectFilter = this.f2ContactManager.getSelectFilter();
    if (filter == F2ContactFilterType.Unread) {
      ToastUtils.showText("已勾选未读联系人");
    } else {
      if (selectFilter == F2ContactFilterType.Unread) {
        ToastUtils.showText("取消勾选未读联系人");
      }
    }
    this.f2ContactManager.setFilter(filter);
    getContext().getApplicationContext().eventHub.emit(ZPEvents.Contact.LoadContact)
  }

  private isSyncing = false;
  syncGeekInfo(){
    if(this.groupType == GroupType.NEW_GREETING){
      if (this.isSyncing) return;
      const securityIds = new Array<string>();
      const contactGroupData = this.f2ContactManager.getContactGroupData(this.groupType);
      if (!contactGroupData) return;
      for (const contactBean of contactGroupData.contactBeans) {
        if (!TextUtils.isEmpty(contactBean.securityId) && TextUtils.isEmpty(contactBean.workYear)
          && TextUtils.isEmpty(contactBean.workDesc)
          && TextUtils.isEmpty(contactBean.degree)
          && TextUtils.isEmpty(contactBean.expectSalary)) {
          securityIds.push(contactBean.securityId);
          //securityIds限制最多300个
          if (securityIds.length >= 300) {
            break;
          }
        }
      }
      if (this.isSyncing) return;
      if(CheckUtils.isNotEmptyArr(securityIds)){
        this.isSyncing = true;
        POST<SyncGeekInfoResponse>(ChatUrlConfig.URL_ZPRELATION_FRIEND_SYNC_GEEKINFO, {
          securityIds: securityIds.join(','),
        }).then(async (resp: HttpResponse<SyncGeekInfoResponse>) => {
          if(resp.zpData){
            const contactDao = await Kernel.getInstance().dbService().getContactDao()
            if(contactDao){
              const contactBeans = new Array<ContactEntity>();
              if(resp.zpData.geekInfoList){
                for (const element of resp.zpData.geekInfoList) {
                  const contactBean = await contactDao.getByFriendIdAndSource(element.geekId,element.geekSource);
                  if (contactBean) {
                    contactBean.workYear = element.workYear;
                    contactBean.workDesc = element.workDesc;
                    contactBean.degree = element.degree;
                    contactBean.expectSalary = element.expectSalary;
                    contactBeans.push(contactBean);
                  }
                };
                await Kernel.getInstance().getContactService().insertAndUpdateContacts([], contactBeans)
              }
            }
          }
          this.isSyncing = false;
        }).catch((reason: ZPHttpError) => {
          this.isSyncing = false;
        })
      }
    }
  }

  @Builder
  ContactListItem(contact: ContactEntity, index: number) {
    if (contact.friendId == FriendType.NewHelloSort) {
      NewHelloSortComponent({
        contact: contact,
        sortType: this.sortType,
        sortClickCallback: (sortType) => {
          this.sortType = sortType
          this.sortNewHello(sortType)
        }
      })
    } else if (contact.friendId == FriendType.TopDivider) {
      TopDividerContactComponent({
        contact: contact,
        expand: this.f2ContactManager.isTopExpandState,
        onExpandClick: () => {
          this.f2ContactManager.isTopExpandState = !this.f2ContactManager.isTopExpandState
          this.freshTimes.setObj(Date.now())
        }
      })
    } else if (contact.friendId == FriendType.NonFoldTopDivider) {
      NonFoldTopDividerContactComponent()
    } else if (contact.friendId == FriendType.WeekAge) {
      WeekAgeContactComponent({ contact: contact, selectedGroupType: this.groupType, })
    } else if (this.f2ContactManager.isSystemContact(contact.friendId)) {
      SystemContactComponent({ contact: contact })
    } else if (contact.friendId == FriendType.Group) {
      GroupContactComponent({ contact: contact })
    } else {
      SingleContactComponent({
        contact: contact,
        index: index,
        groupType: this.groupType,
        sortType: this.sortType
      })
    }
  }

  private sortNewHello(sortType: number){
    ToastUtils.showToast(sortType == ContactGroupBean.RECOMMEND_SORT_TYPE ? "已开启推荐排序" : "已开启时间排序");
    this.f2ContactManager.refreshSortType(GroupType.NEW_GREETING, sortType, () => {
      getContext().getApplicationContext().eventHub.emit(ZPEvents.Contact.LoadContact)
    })
  }

  @Builder
  emptyViewBuilder() {
    Column() {
      Image($r('app.media.ic_empty_page'))
        .width(200)

      Text('暂时还没有消息呢')
        .margin({ top: 20, bottom: 20, left: 40, right: 40 })
        .fontColor($r('app.color.FFB8B8B8_FF6D6D70')).fontSize(16)
    }.height(ZPConstants.FULL_PERCENT)
    .justifyContent(FlexAlign.Center)
  }
}