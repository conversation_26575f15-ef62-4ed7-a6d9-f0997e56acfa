import {
  PagingDataAdapter,
  LoadStateListener,
  WrappedDataItem,
  isPlaceholder
} from 'lib_kernel/src/main/ets/chat/paging/PagingDataAdapter';
import { BasicDataSource, ContactSkeletonItem, SkeletonPresets } from 'lib_zpui/Index';
import { LoadStates, LoadStateType } from 'lib_kernel/src/main/ets/chat/paging/PagingSource';
import { ContactPagingRegistry } from 'lib_kernel/src/main/ets/chat/paging/ContactPagingRegistry';
import { SingleContactComponent } from '../components/SingleContactComponent';
import { TopDividerContactComponent } from '../components/contact_item/TopDividerContactComponent';
import { NonFoldTopDividerContactComponent } from '../components/contact_item/NonFoldTopDividerContactComponent';
import { WeekAgeContactComponent } from '../components/contact_item/WeekAgeContactComponent';
import { NewHelloSortComponent } from '../components/contact_item/NewHelloSortComponent';
import { GroupContactComponent } from '../components/contact_item/GroupContactComponent';
import { ZPEvents } from 'lib_zputils/Index';
import { GroupType } from 'lib_kernel/src/main/ets/chat/db/entity/ContactEntity';
import { Logger, ZPConstants, DateUtils, ZPStore, CheckUtils, TextUtils, ToastUtils } from 'lib_zputils/Index';
import { UnreadStatisticsManager, UnreadStatistics } from 'lib_kernel/src/main/ets/chat/paging/UnreadStatisticsManager';
import { FontColors } from 'lib_zpui';
import { PagingKey } from 'lib_kernel/src/main/ets/chat/paging/OnlyContactPagingSource';
import {
  CommonConfigManager,
  ContactEntity,
  ContactGroupBean,
  ContactMessageTimeRangeRepository,
  F2ContactFilterType,
  F2FriendType as FriendType,
  Kernel
} from 'lib_kernel';
import { SystemContactComponent } from '../components/contact_item/SystemContactComponent';
import { ChatUrlConfig, HttpResponse, POST, ZPHttpError } from 'lib_http/Index';
import { SyncGeekInfoResponse } from '../api/SyncGeekInfoResponse';


const TAG = 'ContactListNewComponent';

// 排序类型枚举，用于传递给子组件
enum ContactSortType {
  DEFAULT = 0
}

/**
 * 联系人列表组件 - 支持"仅沟通"和"全部"tab - 已重构为使用通用分页库
 */
@Component
export struct ContactListNewComponent {
  // 联系人分组类型，默认为仅沟通
  groupType: GroupType = GroupType.ONLY_CONTACT;

  // 筛选状态回调，用于父组件获取筛选状态
  onFilterChanged?: (hasFilter: boolean, filterText: string) => void;

  @State private isInitialized: boolean = false;

  @State private loadStates: LoadStates | null = null;

  @State private errorMessage: string = '';

  @State private isEmpty: boolean = false;

  // Adapter 和 DataSource 现在是核心状态
  private adapter: PagingDataAdapter<PagingKey, ContactEntity> | null = null;

  @State private dataSource: BasicDataSource<WrappedDataItem<ContactEntity>> =
    new BasicDataSource<WrappedDataItem<ContactEntity>>();

  private scroller: Scroller = new Scroller();

  // 控制错误提示的显示
  @State private showErrorToast: boolean = false;

  private errorToastTimer: number = -1;

  // 底部时间范围文案（与ContactsListComponent对齐）
  @State private footerText: string = '';

  // 置顶展开状态管理（从全局状态获取）
  @State private isTopExpanded: boolean = ContactPagingRegistry.getInstance().getGlobalTopExpanded();

  // 未读引导相关状态
  @State private showGuideUnread: boolean = false;

  @State private guideUnreadTip: string = '';

  @State private unreadStats: UnreadStatistics = { unreadContactCount: 0, unreadMessageCount: 0 };

  private unreadStatsManager = UnreadStatisticsManager.getInstance();

  private statsListener?: (allStats: Map<GroupType, UnreadStatistics>) => void;

  // 筛选管理器（与ContactsListComponent共用）
  private f2ContactManager = Kernel.getInstance().getContactService().getF2ContactManager();

  // Boss角色特定功能状态
  @State private sortType: number = ContactGroupBean.NONE_SORT_TYPE;
  private isSyncing: boolean = false;

  async aboutToAppear() {
    this.initialize();
    // 同步全局置顶状态
    this.isTopExpanded = ContactPagingRegistry.getInstance().getGlobalTopExpanded();
    // 加载底部时间范围文案
    this.footerText = await ContactMessageTimeRangeRepository.getInstance().getContactTimeRangeText();

    // 初始化未读引导功能
    await this.initUnreadGuide();

    // 初始化排序类型
    this.initSortType();


    getContext().getApplicationContext().eventHub.on(ZPEvents.Contact.LoadContact, () => {
      // F2ContactManager筛选状态变化时，同步到PagingSource
      this.syncFilterState();
      // 触发数据重新加载
      this.adapter?.invalidateAndReload();
    });

  }

  aboutToDisappear() {
    // registry会管理销毁逻辑
    // 清理可能存在的计时器，防止内存泄漏
    if (this.errorToastTimer !== -1) {
      clearTimeout(this.errorToastTimer);
    }

    // 移除未读统计监听器
    if (this.statsListener) {
      this.unreadStatsManager.removeListener(this.statsListener);
    }
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    Logger.debug(TAG, `initializing component for groupType: ${this.groupType}`);

    try {
      // 1. 从注册表获取适配器（根据groupType选择对应的适配器）
      const adapter = await ContactPagingRegistry.getInstance().getAdapter(this.groupType);
      if (!adapter) {
        throw new Error("Failed to get PagingDataAdapter from registry.");
      }
      this.adapter = adapter;

      // 2. 获取数据源
      this.dataSource = this.adapter.getDataSource();

      // 3. 设置监听器
      this.setupListeners();

      this.syncFilterState();

      // 5. 初始化数据加载
      await this.adapter.initialize();

      this.isInitialized = true;
      this.isEmpty = this.adapter.getItemCount() === 0;
      Logger.debug(TAG, `initialization complete, item count: ${this.adapter.getItemCount()}`);

      // 6. 数据加载完成后，Boss角色再次同步求职者信息
      if (Kernel.getInstance().isBossRole()) {
        this.syncGeekInfo();
      }

    } catch (error) {
      Logger.error(TAG, `initialize failed: ${error}`);
      this.errorMessage = `初始化失败: ${error.message || '未知错误'}`;
    }
  }

  /**
   * 设置监听器
   */
  private setupListeners(): void {
    if (!this.adapter) {
      return;
    }

    // 加载状态监听器
    const loadStateListener: LoadStateListener = {
      onLoadStateChanged: (loadStates: LoadStates) => {
        this.loadStates = loadStates;
        // 更新错误信息
        const refreshError = loadStates.refresh.type === LoadStateType.Error ? loadStates.refresh.error : null;
        const appendError = loadStates.append.type === LoadStateType.Error ? loadStates.append.error : null;
        const prependError = loadStates.prepend.type === LoadStateType.Error ? loadStates.prepend.error : null;

        let errorToShow = '';
        if (refreshError) {
          errorToShow = `刷新失败: ${refreshError.message}`;
        } else if (appendError) {
          errorToShow = `加载更多失败: ${appendError.message}`;
        } else if (prependError) {
          errorToShow = `加载新数据失败: ${prependError.message}`;
        }

        if (errorToShow) {
          this.showError(errorToShow);
        }
      }
    };
    this.adapter.addLoadStateListener(loadStateListener);
  }

  /**
   * 显示错误提示，并在短暂延迟后自动隐藏
   * @param message 要显示的错误消息
   */
  private showError(message: string): void {
    this.errorMessage = message;
    this.showErrorToast = true;

    // 清除上一个计时器（如果有）
    if (this.errorToastTimer !== -1) {
      clearTimeout(this.errorToastTimer);
    }

    // 设置新的计时器
    this.errorToastTimer = setTimeout(() => {
      this.showErrorToast = false;
    }, 3000); // 3秒后自动隐藏
  }

  /**
   * 处理重试
   */
  private async onRetry(): Promise<void> {
    try {
      Logger.debug(TAG, 'retry loading');
      await this.adapter?.retry();
    } catch (error) {
      Logger.error(TAG, `retry failed: ${error}`);
    }
  }

  /**
   * 优化的滚动事件处理
   */
  private onScrollIndexChanged(first: number, last: number): void {
    // 将滚动事件委托给adapter处理
    this.adapter?.onScrollIndex(first, last);
  }

  build() {
    Column() {
      if (!this.isInitialized) {
        this.buildInitialLoading()
      } else if (this.isEmpty) {
        this.buildEmptyState()
      } else {
        this.buildContactList()
      }

      // 错误提示
      if (this.showErrorToast) {
        this.buildErrorToast()
      }


    }
    .width('100%')
    .height('100%')
  }

  /**
   * 构建初始加载状态（与ContactsListComponent对齐）
   */
  @Builder
  buildInitialLoading() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color($r('app.color.FF15B3B3'))

      Text('加载中...')
        .fontSize(14)
        .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
        .margin({ top: 12 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor($r('app.color.FFFEFFFF_FF1D1D1F'))
  }

  /**
   * 构建空状态（与ContactsListComponent对齐）
   */
  @Builder
  buildEmptyState() {
    Column() {
      Image($r('app.media.ic_empty_page'))
        .width(200)

      Text('暂时还没有消息呢')
        .margin({
          top: 20,
          bottom: 20,
          left: 40,
          right: 40
        })
        .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
        .fontSize(16)
    }
    .height(ZPConstants.FULL_PERCENT)
    .justifyContent(FlexAlign.Center)
  }

  /**
   * 构建联系人列表
   */
  @Builder
  buildContactList() {
    List({ scroller: this.scroller }) {
      // 未读引导横幅
      if (this.showGuideUnread) {
        ListItem() {
          this.UnreadGuideBar()
        }
      }

      // 顶部加载指示器
      if (this.loadStates?.prepend.type === LoadStateType.Loading) {
        ListItem() {
          this.buildTopLoadingItem()
        }
      }

      // 顶部错误指示器
      if (this.loadStates?.prepend.type === LoadStateType.Error) {
        ListItem() {
          this.buildTopErrorItem()
        }
      }

      // 联系人列表
      LazyForEach(this.dataSource, (item: WrappedDataItem<ContactEntity>, index: number) => {
        ListItem() {
          if (isPlaceholder(item)) {
            // 占位对象 - 显示骨架屏
            this.buildPlaceholderItem()
          } else {
            // 🔥 新增：判断是否为分割线和特殊联系人类型
            if ((item as ContactEntity).friendId === FriendType.NewHelloSort) {
              // 新招呼排序组件
              NewHelloSortComponent({
                contact: (item as ContactEntity),
                sortType: this.sortType,
                sortClickCallback: (sortType) => {
                  this.sortType = sortType;
                  this.sortNewHello(sortType);
                }
              })
            } else if ((item as ContactEntity).friendId === FriendType.TopDivider) {
              // 可折叠置顶分割线
              TopDividerContactComponent({
                contact: (item as ContactEntity),
                expand: this.isTopExpanded,
                onExpandClick: () => {
                  this.toggleTopExpanded();
                }
              })
            } else if ((item as ContactEntity).friendId === FriendType.NonFoldTopDivider) {
              // 简单置顶分割线
              NonFoldTopDividerContactComponent()
            } else if ((item as ContactEntity).friendId === FriendType.WeekAge) {
              // 一周前消息分割线
              WeekAgeContactComponent({
                contact: (item as ContactEntity),
                selectedGroupType: this.groupType
              })
            } else if (this.f2ContactManager.isSystemContact((item as ContactEntity).friendId)) {
              SystemContactComponent({ contact: (item as ContactEntity) })
            } else if ((item as ContactEntity).friendId === FriendType.Group) {
              // 群组联系人
              GroupContactComponent({ contact: (item as ContactEntity) })
            } else {
              // 普通联系人
              SingleContactComponent({
                contact: (item as ContactEntity),
                index: index,
                groupType: this.groupType,
                sortType: this.sortType
              })
            }
          }
        }
        .width('100%')
      }, (item: WrappedDataItem<ContactEntity>, index: number) => {
        // 为占位对象和真实数据生成不同的key
        if (isPlaceholder(item)) {
          return `placeholder`;
        } else {
          const contact = item as ContactEntity;
          return `${contact.friendId}_${contact.friendSource}_${Math.random()}`;
        }
      })

      // 底部加载指示器
      if (this.loadStates?.append.type === LoadStateType.Loading) {
        ListItem() {
          this.buildBottomLoadingItem()
        }
      }

      // 底部错误指示器
      if (this.loadStates?.append.type === LoadStateType.Error) {
        ListItem() {
          this.buildBottomErrorItem()
        }
      }

      // 底部时间范围文案（与ContactsListComponent对齐）
      if (this.footerText && this.dataSource.totalCount() > 0) {
        ListItem() {
          this.buildFooterText()
        }
      }
    }
    .width('100%')
    .height('100%')
    .scrollBar(BarState.Off)
    .edgeEffect(EdgeEffect.None)
    .onScrollIndex((first: number, last: number) => {
      this.onScrollIndexChanged(first, last);
    })
  }

  /**
   * 构建顶部加载项
   */
  @Builder
  buildTopLoadingItem() {
    Row() {
      LoadingProgress()
        .width(20)
        .height(20)
        .color($r('app.color.FF15B3B3'))

      Text('加载新消息...')
        .fontSize(14)
        .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
        .margin({ left: 8 })
    }
    .width('100%')
    .height(50)
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Center)
    .backgroundColor($r('app.color.FFFEFFFF_FF1D1D1F'))
  }

  /**
   * 构建底部加载项
   */
  @Builder
  buildBottomLoadingItem() {
    Row() {
      LoadingProgress()
        .width(20)
        .height(20)
        .color($r('app.color.FF15B3B3'))

      Text('加载更多...')
        .fontSize(14)
        .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
        .margin({ left: 8 })
    }
    .width('100%')
    .height(50)
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Center)
    .backgroundColor($r('app.color.FFFEFFFF_FF1D1D1F'))
  }

  /**
   * 构建顶部错误项
   */
  @Builder
  buildTopErrorItem() {
    Row() {
      Text('⚠️')
        .fontSize(20)

      Text('加载失败')
        .fontSize(14)
        .fontColor('#FF5E5E5E')
        .margin({ left: 8 })

      Blank()

      Button('重试')
        .fontSize(14)
        .fontColor('#FF15B3B3')
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.onRetry();
        })
    }
    .width('100%')
    .height(50)
    .padding({ left: 16, right: 16 })
    .alignItems(VerticalAlign.Center)
  }

  /**
   * 构建底部错误项
   */
  @Builder
  buildBottomErrorItem() {
    Row() {
      Text('⚠️')
        .fontSize(20)

      Text('加载失败')
        .fontSize(14)
        .fontColor('#FF5E5E5E')
        .margin({ left: 8 })

      Blank()

      Button('重试')
        .fontSize(14)
        .fontColor('#FF15B3B3')
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.onRetry();
        })
    }
    .width('100%')
    .height(50)
    .padding({ left: 16, right: 16 })
    .alignItems(VerticalAlign.Center)
  }

  /**
   * 构建占位对象骨架屏（使用通用组件）
   */
  @Builder
  buildPlaceholderItem() {
    ContactSkeletonItem({ config: SkeletonPresets.DETAILED_CONTACT })
  }

  /**
   * 构建错误提示
   */
  @Builder
  buildErrorToast() {
    Row() {
      Text('⚠️')
        .fontSize(16)

      Text(this.errorMessage)
        .fontSize(14)
        .fontColor(Color.White)
        .margin({ left: 8 })
        .maxLines(2)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
    }
    .padding({
      left: 16,
      right: 16,
      top: 12,
      bottom: 12
    })
    .backgroundColor('#FFFF4753')
    .borderRadius(8)
    .margin({ left: 16, right: 16, bottom: 16 })
    .alignItems(VerticalAlign.Center)
    .position({ x: 0, y: '90%' })
    .zIndex(1000)
  }

  /**
   * 构建底部时间范围文案（与ContactsListComponent对齐）
   */
  @Builder
  buildFooterText() {
    Text(this.footerText)
      .padding(15)
      .fontColor($r('app.color.FFB8B8B8_FF6D6D70'))
      .fontSize(14)
      .backgroundColor($r('app.color.FFF5F5F5_FF262629'))
      .textAlign(TextAlign.Center)
      .width(ZPConstants.FULL_PERCENT)
  }

  /**
   * 切换置顶展开状态
   */
  private toggleTopExpanded(): void {
    this.isTopExpanded = !this.isTopExpanded;

    // 使用统一的全局置顶状态控制
    ContactPagingRegistry.getInstance().setGlobalTopExpanded(this.isTopExpanded);

    // 触发数据重新加载
    this.adapter?.invalidateAndReload();

    Logger.debug(TAG, `top expanded state changed to: ${this.isTopExpanded} for groupType: ${this.groupType}`);
  }

  /**
   * 设置筛选条件（与F2ContactManager同步，添加Toast提示）
   */
  public setFilter(filterType: F2ContactFilterType, jobId?: number): void {
    // 添加Toast提示（与ContactsListComponent对齐）
    const selectFilter = this.f2ContactManager.getSelectFilter();
    if (filterType == F2ContactFilterType.Unread) {
      ToastUtils.showText("已勾选未读联系人");
    } else {
      if (selectFilter == F2ContactFilterType.Unread) {
        ToastUtils.showText("取消勾选未读联系人");
      }
    }

    // 🔥 使用F2ContactManager统一管理筛选状态
    this.f2ContactManager.setFilter(filterType);
    if (jobId && jobId > 0) {
      this.f2ContactManager.setFilterJob(jobId);
    }

    // 使用统一的全局筛选控制
    ContactPagingRegistry.getInstance().setGlobalFilter(filterType, jobId);

    // 触发数据重新加载
    this.adapter?.invalidateAndReload();

    // 通知父组件筛选状态变化
    if (this.onFilterChanged) {
      this.onFilterChanged(this.hasFilter(), this.getFilterText());
    }

    Logger.debug(TAG, `global filter changed: ${filterType}, jobId: ${jobId}`);
  }

  /**
   * 获取当前筛选条件（从F2ContactManager）
   */
  public getCurrentFilter(): F2ContactFilterType {
    return this.f2ContactManager.getSelectFilter();
  }

  /**
   * 是否有筛选条件（从F2ContactManager）
   */
  public hasFilter(): boolean {
    return this.f2ContactManager.hasFilter();
  }

  /**
   * 获取筛选文案（从F2ContactManager）
   */
  public getFilterText(): string {
    const filterType = this.f2ContactManager.getSelectFilter();
    switch (filterType) {
      case F2ContactFilterType.Unread:
        return '只看未读';
      case F2ContactFilterType.Source:
        return '道具筛选';
      default:
        return '筛选';
    }
  }

  /**
   * 初始化时同步筛选状态（使用统一的全局筛选）
   */
  private syncFilterState(): void {
    const currentFilter = this.f2ContactManager.getSelectFilter();
    const jobId = this.f2ContactManager.getSelectJobId();

    // 使用统一的全局筛选控制
    ContactPagingRegistry.getInstance().setGlobalFilter(currentFilter, jobId);

    // 通知父组件
    if (this.onFilterChanged) {
      this.onFilterChanged(this.hasFilter(), this.getFilterText());
    }

    Logger.debug(TAG, `synced global filter state: ${currentFilter}, jobId: ${jobId}`);
  }

  /**
   * 初始化未读引导功能
   */
  private async initUnreadGuide(): Promise<void> {
    // 注册统计数据变化监听
    this.statsListener = (allStats: Map<GroupType, UnreadStatistics>) => {
      const stats = allStats.get(this.groupType);
      if (stats) {
        this.unreadStats = stats;
        // 重新检查是否需要显示未读引导
        this.checkShowUnreadGuide();
      }
    };
    this.unreadStatsManager.addListener(this.statsListener);

    // 获取初始统计数据
    this.unreadStats = await this.unreadStatsManager.getUnreadStatistics(this.groupType);
    await this.checkShowUnreadGuide();
  }

  /**
   * 检查是否显示未读引导
   */
  private async checkShowUnreadGuide(): Promise<void> {
    // 只在"全部"分组显示
    if (this.groupType !== GroupType.ALL) {
      this.showGuideUnread = false;
      return;
    }

    try {
      // 1. 检查未读数量阈值
      const f2ContactUnreadLimit = CommonConfigManager.getInstance().isF2ContactUnreadLimit();
      if (f2ContactUnreadLimit >= 0 && this.unreadStats.unreadContactCount < f2ContactUnreadLimit) {
        this.showGuideUnread = false;
        return;
      }

      // 2. 检查今天是否已显示过
      const lastShowTime = await ZPStore.getValueUid<number>("new_contact_guide_unread_time") ?? 0;
      if (DateUtils.isToday(lastShowTime)) {
        this.showGuideUnread = false;
        return;
      }

      // 3. 检查当前筛选状态
      const currentFilter = ContactPagingRegistry.getInstance().getGlobalFilterType();
      if (currentFilter === 1) { // 已经选择未读筛选
        this.showGuideUnread = false;
        return;
      }

      // 4. 检查是否有未读消息
      if (this.unreadStats.unreadContactCount <= 0) {
        this.showGuideUnread = false;
        return;
      }

      // 满足条件，显示引导
      this.showGuideUnread = true;
      this.generateGuideUnreadTip();

      // 5秒后自动隐藏
      setTimeout(() => {
        this.showGuideUnread = false;
      }, 5000);

    } catch (error) {
      Logger.error(TAG, `checkShowUnreadGuide failed: ${error}`);
      this.showGuideUnread = false;
    }
  }

  /**
   * 生成引导提示文案
   */
  private generateGuideUnreadTip(): void {
    if (this.unreadStats.unreadMessageCount > 0) {
      this.guideUnreadTip = `有超过${this.unreadStats.unreadMessageCount}条未读消息，点击可快速查看`;
    } else {
      this.guideUnreadTip = "有较多未读消息，点击可快速查看";
    }
  }

  /**
   * 未读引导横幅组件
   */
  @Builder
  UnreadGuideBar() {
    Row() {
      Image($r('app.media.ic_resume_close_green'))
        .width(10)
        .height(10)
        .onClick(() => {
          this.showGuideUnread = false;
          ZPStore.setValueUid("new_contact_guide_unread_time", new Date().getTime());
        })

      Text(this.guideUnreadTip)
        .fontSize(13)
        .fontColor($r('app.color.FF0D9EA3'))
        .layoutWeight(1)
        .margin({ left: 8, right: 12 })
        .padding({ top: 8, bottom: 8 })

      Text('查看未读')
        .fontSize(13)
        .fontColor(FontColors.text_white)
        .backgroundColor($r('app.color.FF15B3B3'))
        .padding({
          left: 10,
          right: 10,
          top: 3,
          bottom: 3
        })
        .textAlign(TextAlign.Center)
        .borderRadius(11)
        .onClick(() => {
          this.showGuideUnread = false;
          // 设置未读筛选
          ContactPagingRegistry.getInstance().setGlobalFilter(1); // 1 = 未读筛选
          this.adapter?.invalidateAndReload();
        })
    }
    .backgroundColor($r('app.color.2915B3B3'))
    .padding({ left: 20, right: 20 })
    .width('100%')
  }

  /**
   * 初始化排序类型（与ContactsListComponent对齐）
   */
  private initSortType(): void {
    if (Kernel.getInstance().isBossRole()) {
      this.sortType = this.f2ContactManager.isNewContactRecommendSort(GroupType.NEW_GREETING)
        ? ContactGroupBean.RECOMMEND_SORT_TYPE
        : ContactGroupBean.TIME_SORT_TYPE;
    } else {
      this.sortType = ContactGroupBean.NONE_SORT_TYPE;
    }
  }

  /**
   * 新招呼排序功能（与ContactsListComponent对齐）
   */
  private sortNewHello(sortType: number): void {
    ToastUtils.showToast(sortType == ContactGroupBean.RECOMMEND_SORT_TYPE ? "已开启推荐排序" : "已开启时间排序");
    this.f2ContactManager.refreshSortType(GroupType.NEW_GREETING, sortType, () => {
      getContext().getApplicationContext().eventHub.emit(ZPEvents.Contact.LoadContact);
    });
  }

  /**
   * Boss角色求职者信息同步功能（与ContactsListComponent对齐）
   */
  private syncGeekInfo(): void {
    if (this.groupType == GroupType.NEW_GREETING) {
      if (this.isSyncing) return;

      const securityIds = new Array<string>();
      const contactGroupData = this.f2ContactManager.getContactGroupData(this.groupType);
      if (!contactGroupData) return;

      for (const contactBean of contactGroupData.contactBeans) {
        if (!TextUtils.isEmpty(contactBean.securityId) &&
            TextUtils.isEmpty(contactBean.workYear) &&
            TextUtils.isEmpty(contactBean.workDesc) &&
            TextUtils.isEmpty(contactBean.degree) &&
            TextUtils.isEmpty(contactBean.expectSalary)) {
          securityIds.push(contactBean.securityId);
          // securityIds限制最多300个
          if (securityIds.length >= 300) {
            break;
          }
        }
      }

      if (this.isSyncing) return;
      if (CheckUtils.isNotEmptyArr(securityIds)) {
        this.isSyncing = true;
        POST<SyncGeekInfoResponse>(ChatUrlConfig.URL_ZPRELATION_FRIEND_SYNC_GEEKINFO, {
          securityIds: securityIds.join(','),
        }).then(async (resp: HttpResponse<SyncGeekInfoResponse>) => {
          if (resp.zpData) {
            const contactDao = await Kernel.getInstance().dbService().getContactDao();
            if (contactDao) {
              const contactBeans = new Array<ContactEntity>();
              if (resp.zpData.geekInfoList) {
                for (const element of resp.zpData.geekInfoList) {
                  const contactBean = await contactDao.getByFriendIdAndSource(element.geekId, element.geekSource);
                  if (contactBean) {
                    contactBean.workYear = element.workYear;
                    contactBean.workDesc = element.workDesc;
                    contactBean.degree = element.degree;
                    contactBean.expectSalary = element.expectSalary;
                    contactBeans.push(contactBean);
                  }
                }
                await Kernel.getInstance().getContactService().insertAndUpdateContacts([], contactBeans);
              }
            }
          }
          this.isSyncing = false;
        }).catch((reason: ZPHttpError) => {
          this.isSyncing = false;
        });
      }
    }
  }
}

